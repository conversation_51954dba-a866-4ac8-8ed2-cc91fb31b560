<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120.227" height="48.245" viewBox="0 0 120.227 48.245">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_8054" data-name="Path 8054" d="M1.173-7.268,107.756-7.7l-.733-22.781L2.512-30.266Z" transform="translate(-1.173 30.481)" fill="#fdef00" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_8056" data-name="Path 8056" d="M1.017-12.158l.006-.148.006.151ZM42.6-29.739l-.042-.013,1.908-.006.045.016ZM7.1-6.08l.817.019c-.2,0-.408.013-.61.013S6.9-6.056,6.7-6.061Zm31.6.6c-2.653.051-3.174.054-5.79.032C36.334-5.512,36.334-5.512,38.7-5.484Zm43.8-.292-2.7-.1c-.437-.019-.4-.045.364-.056.576-.008,1.364-.006,2.054,0,.243,0,.593.008.694.014.639.043,1.289.085,1.767.129.1.01-.487.025-.782.039C83.255-5.764,82.753-5.767,82.5-5.776Zm-35.012.334c3.292-.049,6.9-.059,10.5-.068.3,0,.843.012.935.022s-.17.029-.44.032c-3.368.039-6.811.071-10.457.08-.55,0-1.128-.009-2.551-.02Zm-6.1-25.151L46.35-30.6c.522,0,1.128.009,1.565.018a7.7,7.7,0,0,1-1.406.037l-4.034.019c-1.651.006-3.317.012-5.178-.011l-1.262-.106c.759-.011,1.284-.024,1.825-.025l15-.012c.37,0,.748.01,1.364.019-1.342.05-3.111.034-4.646.037H45.03c-1.607,0-3.263-.005-5.447.018ZM73.415-5.269c-.943,0-1.453-.035-.905-.058,1.4-.059,3.879-.058,5.042,0,.282.015.435.032.652.048l8.207,0,2.73,0c3.224-.011,5.028-.083,4.423-.175-.085-.013-.3-.025-.362-.038q-.332-.069,1.909-.092L97.775-5.6c1.384-.012,1.882-.054.935-.082l-5.591-.157-4.37.043-2.14-.14c4.729-.134,9.659-.234,16.508-.221,2.277,0,4.435-.044,5.219-.1a5.142,5.142,0,0,0-1.155-.048l-1.344,0-13.008.062c-2.583.013-5.073.005-7.931-.021.5-.024.707-.048,1.211-.056l4.253-.045,4.284-.043c.925-.011,1.078-.045.417-.066-1.242-.04-2.792-.055-4.522-.037-1.413.015-2.782.035-4.059.058-2.112.038-4.366.066-6.822.067-5.385,0-10.376.063-15.634.083-1.41.005-2.624.018-3.367.058-.878.047-2.529.064-4.139.074l-6.743.031c-.292,0-.679,0-.887,0-.437-.009-1.123-.023-1.085-.033s.626-.031,1.1-.035c.689-.007,1.506-.006,2.242,0,2.293.009,3.79-.018,4.742-.077.25-.016.659-.029.973-.042,4.563-.008,8.022-.081,11.553-.148.539-.01,1.585-.03.73-.066L66.4-6.5c-.788.013-1.594.032-2.428.033-3.245.007-6.127.048-9.185.072L50.8-6.364c-1.866.012-3.306,0-3.928-.059-.129-.012-.393-.024-.673-.034-1.153-.042-2.274-.048-4-.027-2.579.032-4.87.072-5.986.151-.685.049-2.042.073-3.706.089-4.985.049-9.745.121-15.177.125l-.887.006c-2.089.021-4.062.024-6.133-.009-1.7-.027-3.582-.022-5.1.017-.547.015-1.119.024-1.718.029l-1.2.007c0-.11,0-.223.006-.34,0-.279.006-.566.008-.852,0-.325,0-.649,0-.964-.007-.628-.023-1.217-.056-1.692-.1-1.461-.116-3.029-.11-4.767,0-.444-.006-.9-.009-1.339-.047-2.925-.039-5.886-.045-8.841q0-2.438,0-4.879H6.784l5.16-.014c1.149-.005,2.448,0,3.571.012.527.005,1,.031,1.11.05.066.011-.525.031-.982.038-.773.013-1.964.012-2.469.029-1.963.067-4.958.081-7.23.133L3.77-29.513l-1.288,0c.005.314.011.631.016.952.023,1.241.012,2.5-.038,3.666-.062,1.452-.041,2.97-.023,4.476a2.392,2.392,0,0,0,.094.945c.172.046.249-.826.253-2.815,0-.268,0-.538,0-.805-.014-1.989-.028-3.978-.044-6.105a1.912,1.912,0,0,1-.063-.2c.027-.006.072-.012.122-.018.107-.012.206-.022.322-.029.309-.007.6-.012.911-.014.622,0,1.319,0,2.383.02,1.849.03,3.731.032,5.716.032,2.577,0,5.2-.007,7.721,0,3.662.017,7.236.016,10.866,0l9.02-.035,11.785-.028,16.386,0h7.759l-.357-.072c1.386-.029,2.929-.038,4.643-.042l9.013-.029c4.966-.015,9.832-.047,14.918-.041l3.874,0q0,5.469,0,10.953c0,1.432.008,2.868-.034,4.26-.01.317-.006.707,0,1.06,0,.553.056.9.1.679a9.881,9.881,0,0,0,.1-2.108c-.031-1.565-.02-3,.036-4.442.011-.291.021-.8.009-.976a8.9,8.9,0,0,1,.022-1.557c.045-.57.034-1.321.021-2.041-.017-.866-.045-1.748-.042-2.608q0-1.674.008-3.346l-13.528.005H68.027l-5.91,0c-1.655,0-3.228-.015-4.852.009-.632.009-1.5,0-1.813-.033.435-.007.893-.02,1.4-.022l15.784-.062,35.348-.064h.272c.027,3.043.054,6.09.081,9.1a36.006,36.006,0,0,0,.243,4.546,13.306,13.306,0,0,1,.115,1.566c.035.954.07,1.9.112,3.048.034-.411.061-.607.071-.892.043-1.11.105-1.683.211-1.46.064.134.111-.24.159-.752.054-.588.116-1.1.207-1,.012.538.023,1.026.034,1.531q.03,3.2.06,6.409c.022,2.368.088,2.741.263,1.508.013-.135.034-.336.061-.611-.008-.853-.017-1.731-.025-2.6-.015-1.062-.03-2.116-.046-3.174,0-4.173-.12-8.07-.184-12.093-.018-1.132-.039-1.2-.173-.857q-.021-1.081-.043-2.164-.016-1.376-.033-2.761l-6.453.014-5.557-.008c-.411-.017-.87-.03-1.064-.046-.627-.051-1.955-.062-3.609-.06l-4.983,0-4.977.005c-1.716.008-3.3.005-4.916-.011-3-.03-5.9-.036-8.4.043-1.677-.043-2.634-.127-5.567-.071l1.166.054c-2.244.031-4.4.04-6.589.006-2.212-.035-4.5-.038-7.01-.036-4,0-8.021-.013-9.524-.151-.071-.006-.468-.01-.953-.02H38.925c-6.906,0-13.3.079-20.07.1l-4.89.027c-1.918,0-3.761.016-5.578.031l-5.529.047-2.337.012q.021,1.016.042,2.012.024.676.047,1.344c.032.892.065,1.779.1,2.677a16.186,16.186,0,0,0,.214,2.331,8.429,8.429,0,0,1,.069.927c.021.921.043,1.873.065,2.833.011,1.05.022,2.121.033,3.187a6.052,6.052,0,0,1-.11,1.7,5.175,5.175,0,0,0-.144,1.121c-.009.09-.028.036-.062.064-.017-.309-.041-.751-.067-1.228-.03.318-.06.49-.059.657,0,1.47-.046,2.991.034,4.4.024.427.035.992.044,1.514.011.387.021.711.03.993.014.376.032.687.052.963L1.312-5.2c.293-.008,1.15-.008,1.78-.012.11.02.151.038.152.054s-.033.03-.2.042c-.372.025-1.153.044-2.463.059-.009-.3-.02-.675-.032-1.1C.542-6.765.528-7.458.514-8.123.508-9.355.47-10.35.4-11.224c-.026-.442-.061-1.044-.089-1.536-.016-.244-.041-.4-.065-.614-.062.848-.036,1.717-.023,2.417q.022,1.317.046,2.64.019,1.71.04,3.427l9.815-.035L11-4.93l12.624-.047c4.092-.011,8.133-.018,11.9-.074,1.576-.023,3.425-.032,5.2-.047,3.512.083,7.538.077,11.352.046,4.17-.033,8.371-.037,12.595-.048l10.78-.048a23.106,23.106,0,0,0,2.577-.079c.076-.017.195-.034.294-.052l-4.91.01Z" transform="translate(-0.204 30.889)" fill="#fdef00" clip-rule="evenodd"/>
    </clipPath>
  </defs>
  <g id="Group_6290" data-name="Group 6290" transform="translate(-369.773 -268.755)">
    <text id="SQUAREFEET" transform="translate(390 311)" fill="#c6c6c6" font-size="16" font-family="Poppins-Bold, Poppins" font-weight="700"><tspan x="0" y="0">SQUAREFEET</tspan></text>
    <g id="Group_6289" data-name="Group 6289" transform="matrix(1, -0.017, 0.017, 1, 370.108, 301.556)">
      <g id="Group_6287" data-name="Group 6287" transform="translate(2.002 -29.481)" clip-path="url(#clip-path)">
        <path id="Path_8053" data-name="Path 8053" d="M-3.382-2.623H114.783v-32.25H-3.382Z" transform="translate(-3.392 31.217)" fill="#fdef00"/>
      </g>
      <g id="Group_6288" data-name="Group 6288" transform="translate(0.204 -30.889)" clip-path="url(#clip-path-2)">
        <path id="Path_8055" data-name="Path 8055" d="M-4.8-1.413H114.875V-35.889H-4.8Z" transform="translate(-0.204 31.651)" fill="#fdef00"/>
      </g>
    </g>
    <text id="CONCRETE" transform="translate(381 288)" fill="#191919" font-size="16" font-family="Poppins-Bold, Poppins" font-weight="700"><tspan x="0" y="0">CONCRETE</tspan></text>
  </g>
</svg>
