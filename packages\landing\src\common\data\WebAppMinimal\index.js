import envato from 'common/assets/image/webAppMinimal/clients/envato.png';
import evernote from 'common/assets/image/webAppMinimal/clients/evernote.png';
import forbes from 'common/assets/image/webAppMinimal/clients/forbes.png';
import geekwire from 'common/assets/image/webAppMinimal/clients/geekwire.png';
import slack from 'common/assets/image/webAppMinimal/clients/slack.png';
import usaToday from 'common/assets/image/webAppMinimal/clients/usa-today.png';
import dashboardImg from 'common/assets/image/webAppMinimal/dashboard-2.png';
import icon1 from 'common/assets/image/webAppMinimal/icons/1.png';
import icon2 from 'common/assets/image/webAppMinimal/icons/2.png';
import icon3 from 'common/assets/image/webAppMinimal/icons/3.png';
import icon4 from 'common/assets/image/webAppMinimal/icons/4.png';
import asana from 'common/assets/image/webAppMinimal/icons/asana.png';
import donut from 'common/assets/image/webAppMinimal/icons/donut.png';
import dribbble from 'common/assets/image/webAppMinimal/icons/dribbble.png';
import drive from 'common/assets/image/webAppMinimal/icons/drive.png';
import dropbox from 'common/assets/image/webAppMinimal/icons/dropbox.png';
import facebook from 'common/assets/image/webAppMinimal/icons/facebook.png';
import fontAwesome from 'common/assets/image/webAppMinimal/icons/fontawesome.png';
import github from 'common/assets/image/webAppMinimal/icons/github.png';
import googleCloud from 'common/assets/image/webAppMinimal/icons/google-cloud.png';
import icecream from 'common/assets/image/webAppMinimal/icons/icecream.png';
import messenger from 'common/assets/image/webAppMinimal/icons/messenger.png';
import nginx from 'common/assets/image/webAppMinimal/icons/nginx.png';
import pizza from 'common/assets/image/webAppMinimal/icons/pizza.png';
import process1 from 'common/assets/image/webAppMinimal/icons/process1.png';
import process2 from 'common/assets/image/webAppMinimal/icons/process2.png';
import process3 from 'common/assets/image/webAppMinimal/icons/process3.png';
import slack2 from 'common/assets/image/webAppMinimal/icons/slack.png';
import smashingMag from 'common/assets/image/webAppMinimal/icons/smashing-mag.png';
import twitter from 'common/assets/image/webAppMinimal/icons/twitter.png';
import zeplin from 'common/assets/image/webAppMinimal/icons/zeplin.png';
import zoom from 'common/assets/image/webAppMinimal/icons/zoom.png';
import siteLogo from 'common/assets/image/webAppMinimal/logo.svg';
import post1 from 'common/assets/image/webAppMinimal/post1.png';
import post2 from 'common/assets/image/webAppMinimal/post2.png';
import post3 from 'common/assets/image/webAppMinimal/post3.png';
import user1 from 'common/assets/image/webAppMinimal/user1.png';
import user2 from 'common/assets/image/webAppMinimal/user2.png';
import user3 from 'common/assets/image/webAppMinimal/user3.png';

export const menu_items = [
  {
    label: 'Home',
    path: '#home',
    offset: '70',
  },
  {
    label: 'How to',
    path: '#how-to',
    offset: '70',
  },
  {
    label: 'Features',
    path: '#features',
    offset: '70',
  },
  {
    label: 'Testimonial',
    path: '#testimonial',
    offset: '70',
  },
  {
    label: 'Pricing',
    path: '#pricing',
    offset: '70',
  },
  {
    label: 'Faq',
    path: '#faq',
    offset: '70',
  },
];

export const clients = [envato, evernote, forbes, geekwire, slack, usaToday];

export const howTos = [
  {
    id: 1,
    icon: icon1,
    title: 'Manage Smartly',
    text: `Stay on top of your task lists and stay in touch with what's happening`,
    linkLabel: 'Learn More',
    link: '#',
  },
  {
    id: 2,
    icon: icon2,
    title: 'Monitor user Analytics',
    text: `Stay on top of your task lists and stay in touch with what's happening`,
    linkLabel: 'Learn More',
    link: '#',
  },
  {
    id: 3,
    icon: icon3,
    title: 'Safe & Trusted',
    text: `Get the best DoorDash experience with live order tracking.`,
    linkLabel: 'Learn More',
    link: '#',
  },
  {
    id: 4,
    icon: icon4,
    title: 'Fast Customer Support',
    text: `Get the best DoorDash experience with live order tracking.`,
    linkLabel: 'Learn More',
    link: '#',
  },
];

export const analyticsTool = {
  slogan: 'Audience source monitoring',
  title: 'Advanced analytics tools to keep you in control & customizable',
  desc: `Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool.`,
  features: [
    'Chat promt module supported',
    'Enjoy unlimited features by paid plans',
    'Manage ultimate conversation',
  ],
  button: {
    link: '#',
    label: 'Explore More',
  },
};

export const tabs = [
  {
    id: 1,
    nav: {
      icon: process1,
      title: 'User Management',
    },
    content: {
      image: dashboardImg,
    },
  },
  {
    id: 2,
    nav: {
      icon: process2,
      title: 'Online e-Commerce',
    },
    content: {
      image: dashboardImg,
    },
  },
  {
    id: 3,
    nav: {
      icon: process3,
      title: 'Security & Privacy',
    },
    content: {
      image: dashboardImg,
    },
  },
];

export const testimonials = [
  {
    id: 1,
    avatar: user1,
    name: 'Mariana Dickey',
    designation: 'UI Designer',
    quote: `OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design.`,
  },
  {
    id: 2,
    avatar: user2,
    name: 'Jonathan Taylor',
    designation: 'CEO at Creativex',
    quote: `We’re driven beyond just finishing the projects. We want to find solutions with the assessment. OMG! I cannot believe that I have got a brand new landing page.`,
  },
  {
    id: 3,
    avatar: user3,
    name: 'Krish Kendall',
    designation: 'Creative Director',
    quote: `Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool.`,
  },
];

export const appIntegration = {
  sectionTitle: 'Lets see what we integrate',
  sectionDesc: `We recently had to jump on 10+ different plugin across eight different countries to find the right owner and escalation process.`,
  apps: [
    {
      id: 1,
      icon: nginx,
      name: 'nginx',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 2,
      icon: googleCloud,
      name: 'googleCloud',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 3,
      icon: slack2,
      name: 'slack',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 4,
      icon: dropbox,
      name: 'dropbox',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 5,
      icon: drive,
      name: 'drive',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 6,
      icon: asana,
      name: 'asana',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 7,
      icon: github,
      name: 'github',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 8,
      icon: zeplin,
      name: 'zeplin',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 9,
      icon: nginx,
      name: 'nginx',
      bgColor: null,
      isBlurred: true,
    },
    {
      id: 10,
      icon: messenger,
      name: 'messenger',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 11,
      icon: zoom,
      name: 'zoom',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 12,
      icon: smashingMag,
      name: 'smashingMag',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 13,
      icon: fontAwesome,
      name: 'fontAwesome',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 14,
      icon: drive,
      name: 'google drive',
      bgColor: null,
      isBlurred: true,
    },
  ],
};

export const pricing = [
  {
    id: 1,
    price: {
      monthly: 16,
      annual: 16 * 12 - 5,
    },
    currencySymbol: '$',
    isActive: false,
    title: 'Starter Pack',
    desc: 'Complete time tracking & proper reporting',
    icon: icecream,
    button: {
      label: 'Start 6 month trial',
      link: '#',
    },
    details: {
      label: 'What’s include',
      link: '#',
    },
  },
  {
    id: 2,
    price: {
      monthly: 29,
      annual: 29 * 12 - 10,
    },
    currencySymbol: '$',
    isActive: true,
    title: 'Premium Pack',
    desc: 'Effortless team with time management.',
    icon: donut,
    button: {
      label: 'Start 6 month trial',
      link: '#',
    },
    details: {
      label: 'What’s include',
      link: '#',
    },
  },
  {
    id: 3,
    price: {
      monthly: 35,
      annual: 35 * 12 - 15,
    },
    currencySymbol: '$',
    isActive: false,
    title: 'Ultimate Pack',
    desc: 'A custom plan for complex or large organization.',
    icon: pizza,
    button: {
      label: 'Start 6 month trial',
      link: '#',
    },
    details: {
      label: 'What’s include',
      link: '#',
    },
  },
];

export const posts = [
  {
    id: 1,
    date: 'June 3, 2020',
    image: post1,
    title: 'The three Fundamental Rules to Keep Your Website Goal Orientated',
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
  {
    id: 2,
    date: 'Dec 8, 2020',
    image: post2,
    title: 'Five Common Mistakes Teams Make When Tracking Performance',
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
  {
    id: 3,
    date: 'Dec 8, 2020',
    image: post3,
    title: `Why You Might Want to Reconsider with Tracking First Meaningful Paint`,
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
];

export const faqs = [
  {
    id: 1,
    title: '01. What is the process of project final delivery system?',
    description: `Our Customer Experience Team is available 7 days a week and we offer 2 ways to get in contact.Email and Chat. We try to reply quickly, so you need not to wait too long for a response!.`,
  },
  {
    id: 2,
    title: '02. What is payment process, believe in upfront?',
    description: `Please read the documentation carefully. We also have some online  video tutorials regarding this issue. If the problem remains, Please Open a ticket in the support forum.`,
  },
  {
    id: 3,
    title: '03. What is the process of project final delivery system?',
    description: `At first, Please check your internet connection. We also have some online  video tutorials regarding this issue. If the problem remains, Please Open a ticket in the support forum.`,
  },
  {
    id: 4,
    title: '04. Estimate project budget for categories?',
    description: `Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, startup's, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.`,
  },
  {
    id: 5,
    title: '05. All about project customization & monetization',
    description: `We are giving the update of this theme continuously. You will receive an email Notification when we push an update. Always try to be updated with us.`,
  },
];

export const footerTop = {
  about: {
    logo: siteLogo,
    text: `We run Advanced Search reports on the criteria you care about to see how work is progressing and where to focus your effort.`,
  },
  widgets: [
    {
      id: 2,
      title: 'About Us',
      list: [
        {
          id: 1,
          title: 'Support Center',
          link: '#',
        },
        {
          id: 2,
          title: 'Customer Support',
          link: '#',
        },
        {
          id: 3,
          title: 'About Us',
          link: '#',
        },
        {
          id: 4,
          title: 'Copyright',
          link: '#',
        },
        {
          id: 5,
          title: 'Popular Campaign',
          link: '#',
        },
      ],
    },
    {
      id: 3,
      title: 'Our Information',
      list: [
        {
          id: 1,
          title: 'Return Policy ',
          link: '#',
        },
        {
          id: 2,
          title: 'Privacy Policy',
          link: '#',
        },
        {
          id: 3,
          title: 'Terms & Conditions',
          link: '#',
        },
        {
          id: 4,
          title: 'Site Map',
          link: '#',
        },
        {
          id: 5,
          title: 'Store Hours',
          link: '#',
        },
      ],
    },
    {
      id: 4,
      title: 'My Account',
      list: [
        {
          id: 1,
          title: 'Press inquiries',
          link: '#',
        },
        {
          id: 2,
          title: 'Social media ',
          link: '#',
        },
        {
          id: 3,
          title: 'directories',
          link: '#',
        },
        {
          id: 4,
          title: 'Images & B-roll',
          link: '#',
        },
        {
          id: 5,
          title: 'Permissions',
          link: '#',
        },
      ],
    },
  ],
  contactInfo: {
    title: 'Contact info',
    address: `Amsterdam, Netherlands`,
    phone: `+31 62 19 22 705`,
    openingTime: `7 Days - 8am - 10pm`,
    email: `<EMAIL>`,
  },
};

export const footer = {
  copyright: `Copyright © 2021 Superprops. All rights reserved`,
  nav: [
    {
      id: 1,
      title: 'Support',
      link: '#',
    },
    {
      id: 2,
      title: 'Hiring',
      link: '#',
    },
    {
      id: 3,
      title: 'Privacy',
      link: '#',
    },
    {
      id: 4,
      title: 'Terms',
      link: '#',
    },
  ],
  socialLinks: [
    {
      id: 1,
      link: 'http://facebook.com',
      icon: facebook,
      label: 'Facebook',
    },
    {
      id: 2,
      link: 'http://twitter.com',
      icon: twitter,
      label: 'Twitter',
    },
    {
      id: 3,
      link: 'http://dribbble.com',
      icon: dribbble,
      label: 'Dribbble',
    },
  ],
};
