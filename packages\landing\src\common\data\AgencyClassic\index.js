import Screenshot1 from '../../assets/image/saasClassic/screen-1.png';

import AuthorOne from '../../assets/image/saasClassic/author-1.jpg';
import AuthorTwo from '../../assets/image/saasClassic/author-2.jpg';
import AuthorThree from '../../assets/image/saasClassic/author-3.jpg';

import { ic_monetization_on } from 'react-icons-kit/md/ic_monetization_on';
import { ic_settings } from 'react-icons-kit/md/ic_settings';
import { pieChart } from 'react-icons-kit/icomoon/pieChart';
import { briefcase } from 'react-icons-kit/fa/briefcase';

import evernote from '../../assets/image/agencyClassic/clients/evernote.svg';
import google from '../../assets/image/agencyClassic/clients/Google.svg';
import dribble from '../../assets/image/agencyClassic/clients/dribble.svg';
import microsoft from '../../assets/image/agencyClassic/clients/microsoft.svg';
import paypal from '../../assets/image/agencyClassic/clients/Paypal.svg';
import uber from '../../assets/image/agencyClassic/clients/Uber.svg';


import coding from '../../assets/image/agencyClassic/services/coding.png';
import creative from '../../assets/image/agencyClassic/services/creative.png';
import logo from '../../assets/image/agencyClassic/services/logo.png';
import search from '../../assets/image/agencyClassic/services/search.png';
import theme from '../../assets/image/agencyClassic/services/theme.png';
import ux from '../../assets/image/agencyClassic/services/user-experience.png';

import supportPerson1 from '../../assets/image/agencyClassic/support/person1.svg';
import supportPerson2 from '../../assets/image/agencyClassic/support/person2.svg';
import supportPerson3 from '../../assets/image/agencyClassic/support/person3.svg';
import supportPerson4 from '../../assets/image/agencyClassic/support/person4.svg';
import supportPerson5 from '../../assets/image/agencyClassic/support/person5.svg';
import supportPerson6 from '../../assets/image/agencyClassic/support/person6.svg';

import CS1 from '../../assets/image/agencyClassic/caseStudy/image1.png';
import CS2 from '../../assets/image/agencyClassic/caseStudy/image2.png';
import CS3 from '../../assets/image/agencyClassic/caseStudy/image3.png';
import CS4 from '../../assets/image/agencyClassic/caseStudy/image4.png';

import Marketing1 from '../../assets/image/agencyClassic/marketing/img1.png';
import Marketing2 from '../../assets/image/agencyClassic/marketing/img2.png';
import Marketing3 from '../../assets/image/agencyClassic/marketing/img3.png';
import Marketing4 from '../../assets/image/agencyClassic/marketing/img4.png';

import testimonialAuthor1 from '../../assets/image/agencyClassic/testimonial/img.png';
import testimonialBrand1 from '../../assets/image/agencyClassic/testimonial/logo.png';

export const MENU_ITEMS = [
  {
    label: 'Home',
    path: '#banner_section',
    offset: '0',
  },
  {
    label: 'Product',
    path: '#product_section',
    offset: '0',
  },
  {
    label: 'Testimonial',
    path: '#testimonial_section',
    offset: '0',
  },
  {
    label: 'About',
    path: '#about_section',
    offset: '0',
  },
];

export const clients = [uber, google, paypal, microsoft, dribble, evernote];

export const FEATURES = [
  {
    icon: search,
    title: 'Digital Strategy',
    description:
      'We start each new digital product design partnershipwith an in‑depth discovery phase to immerse ourselves in your business.',
  },
  {
    icon: ux,
    title: 'User Experience',
    description:
      'We start each new digital product design partnershipwith an in‑depth discovery phase to immerse ourselves in your business.',
  },
  {
    icon: logo,
    title: 'Branding',
    description:
      'We start each new digital product design partnershipwith an in‑depth discovery phase to immerse ourselves in your business.',
  },
  {
    icon: coding,
    title: 'Development',
    description:
      'We start each new digital product design partnershipwith an in‑depth discovery phase to immerse ourselves in your business.',
  },
  {
    icon: theme,
    title: 'Web Design',
    description:
      'We start each new digital product design partnershipwith an in‑depth discovery phase to immerse ourselves in your business.',
  },
  {
    icon: creative,
    title: 'Product Position',
    description:
      'We start each new digital product design partnershipwith an in‑depth discovery phase to immerse ourselves in your business.',
  },
];

export const SUPPORT = [
  {
    person: supportPerson1
  },
  {
    person: supportPerson2
  },
  {
    person: supportPerson3
  },
  {
    person: supportPerson4
  },
  {
    person: supportPerson5
  },
  {
    person: supportPerson6
  },{
    person: supportPerson1
  },
  {
    person: supportPerson2
  },
  {
    person: supportPerson3
  },
  {
    person: supportPerson4
  },
  {
    person: supportPerson5
  },
  {
    person: supportPerson6
  },
]

export const CASESTUDY = [
  {
    image: CS1,
    subheading: 'Branding',
    heading: 'Improving a business titan through design & strategy'
  },
  {
    image: CS2,
    subheading: 'User Interface',
    heading: 'Bringing a modern mobile experience to learning'
  },
  {
    image: CS3,
    subheading: 'Redesign Chellange',
    heading: 'Delivering digital excellence by simplifying the customer experience anytime'
  },
  {
    image: CS4,
    subheading: 'User Experience',
    heading: 'Art Direction for leading Melbourne publishing company'
  }
]

export const TESTIMONIALS = [
  {
    title: 'Modern look & trending design',
    review:
      'OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design. We deliver on such an expansive with innovation agenda with so many theme projects.',
    name: 'Johnny Simpson',
    designation: 'Head of Design by GeekWire',
    avatar: testimonialAuthor1,
    brand: testimonialBrand1
  },
  {
    title: 'Modern look & trending design',
    review:
      'OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design. We deliver on such an expansive with innovation agenda with so many theme projects.',
    name: 'Johnny Simpson',
    designation: 'Head of Design by GeekWire',
    avatar: testimonialAuthor1,
    brand: testimonialBrand1
  },
  {
    title: 'Modern look & trending design',
    review:
      'OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design. We deliver on such an expansive with innovation agenda with so many theme projects.',
    name: 'Johnny Simpson',
    designation: 'Head of Design by GeekWire',
    avatar: testimonialAuthor1,
    brand: testimonialBrand1
  }
];

export const TEAM = [
  {
    title: 'Professional Environment',
    description:
      'We enjoy office retreats and visit famous places of our country and abroad also! We have planned to extend our business.'
  },
  {
    title: 'Worked with experienced team',
    description:
      'With more than 100,000 users, we are is growing fast. Currently, we are a team of 65+ people'
  },
  {
    title: 'Automate your marketing',
    description:
      'With more than 100,000 users, we are is growing fast. Currently, we are a team of 65+ people'
  },
  {
    title: 'Ultimate skill development',
    description:
      'Life is what happens when you’re busy working. We make sure you enjoy it with ultimate experience.'
  },
  {
    title: 'Flexible working experience',
    description:
      'We build amazing things that work with awesome people who are WordPress enthusiasts, talented, eager to learn.'
  },
  {
    title: 'Reach the right people',
    description:
      'We build amazing things that work with awesome people who are WordPress enthusiasts, talented, eager.'
  },
];

export const MARKETING = [
  {
    image: Marketing1,
    date: 'December 15, 2021',
    title: 'How to Boost Your Response Rate with this Simple Copywriting Trick easily & workfully',
  },
  {
    image: Marketing2,
    date: 'January 24, 2022',
    title: 'Need podcast branding advice? These Spotify marketing pros can help',
  },
  {
    image: Marketing3,
    date: 'February 11, 2022',
    title: '4 ways to make podcast analytics part of your show’s growth strategy',
  },
  {
    image: Marketing4,
    date: 'September 18, 2022',
    title: 'How to use Q&A and Polls to increase audience engagement for your podcast',
  }
]

export const FAQ_DATA = [
  {
    expend: true,
    title: 'What is the process of project final delivery system?',
    description:
      'Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, Startupers, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.',
  },
  {
    title: 'What is payment process, believe in upfront?',
    description:
      'Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, Startupers, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.',
  },
  {
    title: 'What is the process of project final delivery system?',
    description:
      'Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, Startupers, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.',
  },
  {
    title: 'Estimate project budget for categories?',
    description:
      'Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, Startupers, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.',
  },
  {
    title: 'All about project customization & monitaization',
    description:
      'Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, Startupers, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.',
  }
];


export const FOOTER = [
  {
    id: 1,
    title: 'About Us',
    list: [
      {
        id: 1,
        title: 'Support Center',
        link: '#',
      },
      {
        id: 2,
        title: 'Customer Support',
        link: '#',
      },
      {
        id: 3,
        title: 'About Us',
        link: '#',
      },
      {
        id: 4,
        title: 'Copyright',
        link: '#',
      },
      {
        id: 5,
        title: 'Popular Campaign',
        link: '#',
      },
    ],
  },
  {
    id: 2,
    title: 'Data Policy',
    list: [
      {
        id: 1,
        title: 'Our Data',
        link: '#',
      },
      {
        id: 2,
        title: 'Verify Your Website',
        link: '#',
      },
      {
        id: 3,
        title: 'Browser Extension',
        link: '#',
      },
      {
        id: 4,
        title: 'Privacy and Security',
        link: '#',
      }
    ],
  },
  {
    id: 3,
    title: 'Our Information',
    list: [
      {
        id: 1,
        title: 'Return Policy ',
        link: '#',
      },
      {
        id: 2,
        title: 'Privacy Policy',
        link: '#',
      },
      {
        id: 3,
        title: 'Terms & Conditions',
        link: '#',
      },
      {
        id: 4,
        title: 'Site Map',
        link: '#',
      },
      {
        id: 5,
        title: 'Store Hours',
        link: '#',
      },
    ],
  },
  {
    id: 4,
    title: 'My Account',
    list: [
      {
        id: 1,
        title: 'Press inquiries',
        link: '#',
      },
      {
        id: 2,
        title: 'Social media ',
        link: '#',
      },
      {
        id: 3,
        title: 'directories',
        link: '#',
      },
      {
        id: 4,
        title: 'Images & B-roll',
        link: '#',
      },
      {
        id: 5,
        title: 'Permissions',
        link: '#',
      },
    ],
  }
];

export const CONTACT_INFO = [
  {
    title: 'Contact info',
    address: `Mohakhali DOHS, Amsterdam, Netherlands`,
    phone: `+31 62 19 22 705`,
    openingTime: `7 Days - 8am - 10pm`,
    email: `<EMAIL>`,
  }
];

export const FOOTER_NAV = [
  {
    id: 1,
    title: 'Legal',
    link: '#',
  },
  {
    id: 2,
    title: 'Changelog',
    link: '#',
  },
  {
    id: 3,
    title: 'Support',
    link: '#',
  },
];

export const FOOTER_WIDGET = [
  {
    title: 'About Us',
    menuItems: [
      {
        url: '#',
        text: 'Support Center',
      },
      {
        url: '#',
        text: 'Customer Support',
      },
      {
        url: '#',
        text: 'About Us',
      },
      {
        url: '#',
        text: 'Copyright',
      },
      {
        url: '#',
        text: 'Popular Campaign',
      },
    ],
  },
  {
    title: 'Our Information',
    menuItems: [
      {
        url: '#',
        text: 'Return Policy',
      },
      {
        url: '#',
        text: 'Privacy Policy',
      },
      {
        url: '#',
        text: 'Terms & Conditions',
      },
      {
        url: '#',
        text: 'Site Map',
      },
      {
        url: '#',
        text: 'Store Hours',
      },
    ],
  },
  {
    title: 'My Account',
    menuItems: [
      {
        url: '#',
        text: 'Press inquiries',
      },
      {
        url: '#',
        text: 'Social media directories',
      },
      {
        url: '#',
        text: 'Images & B-roll',
      },
      {
        url: '#',
        text: 'Permissions',
      },
      {
        url: '#',
        text: 'Speaker requests',
      },
    ],
  },
  {
    title: 'Policy',
    menuItems: [
      {
        url: '#',
        text: 'Application security',
      },
      {
        url: '#',
        text: 'Software principles',
      },
      {
        url: '#',
        text: 'Unwanted software policy',
      },
      {
        url: '#',
        text: 'Responsible supply chain',
      },
    ],
  },
];











export const SERVICE_ITEMS = [
  {
    icon: 'flaticon-stopwatch-1',
    title: 'Fast Performance',
  },
  {
    icon: 'flaticon-prototype',
    title: 'Prototyping',
  },
  {
    icon: 'flaticon-code',
    title: 'Coade Export',
  },
  {
    icon: 'flaticon-vectors',
    title: 'Vector Editing',
  },
  {
    icon: 'flaticon-export',
    title: 'Export Presets',
  },
];

export const MONTHLY_PRICING_TABLE = [
  {
    name: 'Basic Account',
    description: 'For Small teams or group who need to build website ',
    price: '$0',
    priceLabel: 'Only for first month',
    buttonLabel: 'Start for free',
    url: '#',
    listItems: [
      {
        content: 'Drag & Drop Builder',
      },
      {
        content: '1,000s of Templates Ready',
      },
      {
        content: 'Blog Tools',
      },
      {
        content: 'eCommerce Store ',
      },
      {
        content: '30+ Webmaster Tools',
      },
    ],
  },
  {
    name: 'Business Account',
    description: 'For Mediums teams or group who need to build website ',
    price: '$9.87',
    priceLabel: 'Per month & subscription yearly',
    buttonLabel: 'Register Now',
    url: '#',
    trialButtonLabel: 'Or Start 14 Days trail',
    trialURL: '#',
    listItems: [
      {
        content: 'Drag & Drop Builder',
      },
      {
        content: '1,000s of Templates Ready',
      },
      {
        content: 'Blog Tools',
      },
      {
        content: 'eCommerce Store ',
      },
      {
        content: '30+ Webmaster Tools',
      },
    ],
  },
  {
    name: 'Premium Account',
    description: 'For Large teams or group who need to build website ',
    price: '$12.98',
    priceLabel: 'Per month & subscription yearly',
    buttonLabel: 'Register Now',
    url: '#',
    trialButtonLabel: 'Or Start 14 Days trail',
    trialURL: '#',
    listItems: [
      {
        content: 'Drag & Drop Builder',
      },
      {
        content: '1,000s of Templates Ready',
      },
      {
        content: 'Blog Tools',
      },
      {
        content: 'eCommerce Store ',
      },
      {
        content: '30+ Webmaster Tools',
      },
    ],
  },
];

export const YEARLY_PRICING_TABLE = [
  {
    name: 'Basic Account',
    description: 'For a single client or team who need to build website ',
    price: '$0',
    priceLabel: 'Only for first month',
    buttonLabel: 'Start for free',
    url: '#',
    listItems: [
      {
        content: 'Drag & Drop Builder',
      },
      {
        content: '1,000s of Templates Ready',
      },
      {
        content: 'Blog Tools',
      },
      {
        content: 'eCommerce Store ',
      },
      {
        content: '30+ Webmaster Tools',
      },
    ],
  },
  {
    name: 'Business Account',
    description: 'For Small teams or group who need to build website ',
    price: '$6.00',
    priceLabel: 'Per month & subscription yearly',
    buttonLabel: 'Register Now',
    url: '#',
    trialButtonLabel: 'Or Start 14 Days trail',
    trialURL: '#',
    listItems: [
      {
        content: 'Unlimited secure storage',
      },
      {
        content: '2,000s of Templates Ready',
      },
      {
        content: 'Blog Tools',
      },
      {
        content: '24/7 phone support',
      },
      {
        content: '50+ Webmaster Tools',
      },
    ],
  },
  {
    name: 'Premium Account',
    description: 'For Large teams or group who need to build website ',
    price: '$9.99',
    priceLabel: 'Per month & subscription yearly',
    buttonLabel: 'Register Now',
    url: '#',
    trialButtonLabel: 'Or Start 14 Days trail',
    trialURL: '#',
    listItems: [
      {
        content: 'Drag & Drop Builder',
      },
      {
        content: '3,000s of Templates Ready',
      },
      {
        content: 'Advanced branding',
      },
      {
        content: 'Knowledge base support',
      },
      {
        content: '80+ Webmaster Tools',
      },
    ],
  },
];

export const SCREENSHOTS = [
  {
    icon: ic_monetization_on,
    title: 'Budget Overview',
    image: Screenshot1,
  },
  {
    icon: ic_settings,
    title: 'Create & Adjust',
    image: Screenshot1,
  },
  {
    icon: pieChart,
    title: 'View Reports',
    image: Screenshot1,
  },
  {
    icon: briefcase,
    title: 'Integrations',
    image: Screenshot1,
  },
];
