import logo1 from '../../assets/image/saasMinimal2/clients/1.png';
import logo2 from '../../assets/image/saasMinimal2/clients/2.png';
import logo3 from '../../assets/image/saasMinimal2/clients/3.png';
import logo4 from '../../assets/image/saasMinimal2/clients/4.png';
import logo5 from '../../assets/image/saasMinimal2/clients/5.png';
import logo6 from '../../assets/image/saasMinimal2/clients/6.png';
import process1 from '../../assets/image/saasMinimal2/dashboard/1.png';
import process2 from '../../assets/image/saasMinimal2/dashboard/2.png';
import process3 from '../../assets/image/saasMinimal2/dashboard/3.png';
import feature1 from '../../assets/image/saasMinimal2/features/1.png';
import feature2 from '../../assets/image/saasMinimal2/features/2.png';
import feature3 from '../../assets/image/saasMinimal2/features/3.png';
import feature4 from '../../assets/image/saasMinimal2/features/4.png';
import dribbble from '../../assets/image/saasMinimal2/social/dribbble.png';
import fb from '../../assets/image/saasMinimal2/social/fb.png';
import twitter from '../../assets/image/saasMinimal2/social/twitter.png';
import author1 from '../../assets/image/saasMinimal2/testimonials/1.png';
import author2 from '../../assets/image/saasMinimal2/testimonials/2.png';


export const data = {
  navItems: [
    {
      label: 'Home',
      path: '#home',
      offset: '70',
    },
    {
      label: 'Feature',
      path: '#features',
      offset: '70',
    },
    {
      label: 'Pricing',
      path: '#pricing',
      offset: '70',
    },
    {
      label: 'Dashboard',
      path: '#dashboard',
      offset: '70',
    },
    {
      label: 'Testimonials',
      path: '#testimonials',
      offset: '70',
    },
    {
      label: 'Login now',
      path: '#home',
      staticLink: true,
    },
    {
      label: 'Join Free',
      path: '#home',
      staticLink: true,
    },
  ],
  features: [
    {
      id: 1,
      icon: feature1,
      title: 'Analytics Report',
      desc: `We’re driven beyond just finishing the projects. We want to find smart solutions.`,
    },
    {
      id: 2,
      icon: feature2,
      title: 'User Customization',
      desc: `We’re driven beyond just finishing the projects. We want to find smart solutions.`,
    },
    {
      id: 3,
      icon: feature3,
      title: 'Help & Support',
      desc: `We’re driven beyond just finishing the projects. We want to find smart solutions.`,
    },
    {
      id: 4,
      icon: feature4,
      title: 'Use Accessibility',
      desc: `We’re driven beyond just finishing the projects. We want to find smart solutions.`,
    },
  ],
  clients: [
    {
      id: 1,
      logo: logo1,
      name: 'Slack',
    },
    {
      id: 2,
      logo: logo2,
      name: 'Leo',
    },
    {
      id: 3,
      logo: logo3,
      name: 'Oimenu',
    },
    {
      id: 4,
      logo: logo4,
      name: 'Subway',
    },
    {
      id: 5,
      logo: logo5,
      name: 'Visao',
    },
    {
      id: 6,
      logo: logo6,
      name: 'OralUnic',
    },
  ],
  statistics: [
    {
      id: 1,
      value: 15,
      url: '#',
      title: 'average increase in bookings',
    },
    {
      id: 2,
      value: 73,
      url: '#',
      title: 'increase in free account conversions',
    },
    {
      id: 3,
      value: 28,
      url: '#',
      title: 'increase in reservations',
    },
  ],
  dashboardProcess: [
    {
      id: 1,
      icon: process1,
      title: 'User Management',
    },
    {
      id: 2,
      icon: process2,
      title: 'Online e-Commerce',
    },
    {
      id: 3,
      icon: process3,
      title: 'Security & Privacy',
    },
  ],
  testimonials: [
    {
      id: 1,
      photo: author1,
      rating: 4,
      name: 'Jonathan Taylor',
      designation: 'CEO at Creativex',
      text: `“OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design.`,
    },
    {
      id: 2,
      photo: author2,
      rating: 3,
      name: 'Andree Jaction',
      designation: 'COO at Uber co.',
      text: `“My company has changed completely after I started using Coalize solutions. My employees are more motivated and I have managed to increase talent retention with the happiest service.`,
    },
    {
      id: 3,
      photo: author1,
      rating: 5,
      name: 'Andree Jaction',
      designation: 'COO at Uber co.',
      text: `“My company has changed completely after I started using Coalize solutions. My employees are more motivated and I have managed to increase talent retention with the happiest service.`,
    },
    {
      id: 4,
      photo: author2,
      rating: 5,
      name: 'Andree Jaction',
      designation: 'COO at Uber co.',
      text: `“My company has changed completely after I started using Coalize solutions. My employees are more motivated and I have managed to increase talent retention with the happiest service.`,
    },
  ],
  footerNav: [
    {
      id: 1,
      url: '#',
      title: 'Support',
    },
    {
      id: 2,
      url: '#',
      title: 'About Us',
    },
    {
      id: 3,
      url: '#',
      title: 'Privacy',
    },
    {
      id: 4,
      url: '#',
      title: 'Contact',
    },
  ],
  socialLinks: [
    {
      id: 1,
      icon: fb,
      link: '#',
      label: 'Facebook',
    },
    {
      id: 2,
      icon: twitter,
      link: '#',
      label: 'Twitter',
    },
    {
      id: 3,
      icon: dribbble,
      link: '#',
      label: 'Dribbble',
    },
  ],
};
