{"MENU_ITEMS": [{"label": "Home", "path": "#banner_section", "offset": "0"}, {"label": "Feature", "path": "#trusted", "offset": "0"}, {"label": "Offers", "path": "#scalable", "offset": "-10"}, {"label": "Payment Proofs", "path": "#featureslider", "offset": "-10"}, {"label": "Contact Us", "path": "#footerSection", "offset": "380"}], "TESTIMONIALS": [{"review": "Best working experience  with this amazing team & in future, we want to work together", "name": "<PERSON>", "designation": "CEO of Dell Co.", "avatar": "../../assets/image/crypto/author-4.jpg"}, {"review": "Impressed with master class support of the team and really look forward for the future.", "name": "<PERSON>", "designation": "Co Founder of IBM", "avatar": "../../assets/image/crypto/author-2.jpg"}, {"review": "I have bought more than 10 themes on ThemeForest, and this is the first one I review.", "name": "<PERSON><PERSON>", "designation": "Manager of Hp co.", "avatar": "../../assets/image/crypto/author-3.jpg"}, {"review": "Impressed with master class support of the team and really look forward for the future.", "name": "<PERSON>", "designation": "Manager of Hp co.", "avatar": "../../assets/image/crypto/author-1.jpg"}], "TRANSACTIONS_FEATURE": [{"image": "../../assets/image/crypto/tf1.svg", "title": "Create Payment Address", "des": "Provide your payout wallet address and callback URL to PayBear API."}, {"image": "../../assets/image/crypto/tf2.svg", "title": "Ask for Payment", "des": "Show your customer the wallet address as well as the payment amount."}, {"image": "../../assets/image/crypto/tf3.svg", "title": "Get Paid", "des": "Payment is sent to the payout wallet immediately."}, {"image": "../../assets/image/crypto/tf4.svg", "title": "Get Payment Notification.", "des": "Callbacks are sent to the URL you specified. You can process customer order"}], "PROOFS_FEATURE": [{"image": "../../assets/image/crypto/proof1.svg", "title": "Instant trading", "des": "Never miss a price swing."}, {"image": "../../assets/image/crypto/proof2.svg", "title": "No hidden fees", "des": "know our fees upfront."}, {"image": "../../assets/image/crypto/proof3.svg", "title": "Secure storage", "des": "Sleep with peace of mind."}, {"image": "../../assets/image/crypto/proof4.svg", "title": "Systematic trading", "des": "History intraday market."}, {"image": "../../assets/image/crypto/proof5.svg", "title": "Network Effect", "des": "Casinos contribute 1%."}, {"image": "../../assets/image/crypto/proof6.svg", "title": "Bigger <PERSON>s", "des": "Players are incentivized."}], "SCALABLE_FEATURE": [{"image": "../../assets/image/crypto/jackpot.svg", "title": "Daily Jackpot", "des": "35000 CLV"}, {"image": "../../assets/image/crypto/jackpot.svg", "title": "Weekly Jackpot", "des": "250000 CLV"}, {"image": "../../assets/image/crypto/jackpot.svg", "title": "Monthly Jackpot", "des": "4999697 CLV"}, {"image": "../../assets/image/crypto/jackpot.svg", "title": "Yearly Jackpot", "des": "300245785000 CLV"}], "BETA_FEATURE": [{"image": "../../assets/image/crypto/beta-1.svg", "title": "SEPA Transfers", "des": "Deposit & Withdraw money."}, {"image": "../../assets/image/crypto/beta-2.svg", "title": "24/7 Support", "des": "Always here for you."}, {"image": "../../assets/image/crypto/beta-3.svg", "title": "Secure", "des": "Your money is safe."}], "menuWidget": [{"id": 1, "title": "About Us", "menuItems": [{"id": 1, "url": "#", "text": "Support Center"}, {"id": 2, "url": "#", "text": "Customer Support"}, {"id": 3, "url": "#", "text": "About Us"}, {"id": 4, "url": "#", "text": "Copyright"}, {"id": 5, "url": "#", "text": "Popular Campaign"}]}, {"id": 2, "title": "Our Information", "menuItems": [{"id": 1, "url": "#", "text": "Return Policy"}, {"id": 2, "url": "#", "text": "Privacy Policy"}, {"id": 3, "url": "#", "text": "Terms & Conditions"}, {"id": 4, "url": "#", "text": "Site Map"}, {"id": 5, "url": "#", "text": "Store Hours"}]}, {"id": 3, "title": "My Account", "menuItems": [{"id": 1, "url": "#", "text": "Press inquiries"}, {"id": 2, "url": "#", "text": "Social media directories"}, {"id": 3, "url": "#", "text": "Images & B-roll"}, {"id": 4, "url": "#", "text": "Permissions"}, {"id": 5, "url": "#", "text": "Speaker requests"}]}], "Language_NAMES": [{"label": "English", "value": "eng"}, {"label": "Chinese", "value": "chinese"}, {"label": "Indian", "value": "indian"}]}