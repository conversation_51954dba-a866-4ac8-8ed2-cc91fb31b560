<svg xmlns="http://www.w3.org/2000/svg" width="160" height="150" viewBox="0 0 160 150">
  <defs>
    <style>
      .cls-1 {
        fill: #f5b29c;
      }

      .cls-2 {
        fill: #b9b3c0;
      }

      .cls-3 {
        fill: #0b1238;
      }

      .cls-14, .cls-4 {
        fill: #fff;
      }

      .cls-5 {
        fill: #40528e;
      }

      .cls-6 {
        fill: #f59c6f;
      }

      .cls-7 {
        fill: #596a83;
      }

      .cls-8 {
        fill: #5c75c3;
      }

      .cls-9 {
        fill: #dde4ea;
      }

      .cls-10 {
        fill: #2e3054;
      }

      .cls-11 {
        fill: #7c9aa9;
      }

      .cls-12, .cls-14 {
        opacity: 0.4;
      }

      .cls-13 {
        fill: #7ab6f6;
      }

      .cls-15 {
        fill: #fa6082;
      }
    </style>
  </defs>
  <g id="service_1" data-name="service 1" transform="translate(-9.784 0.011)">
    <g id="Layer_2" data-name="Layer 2" transform="translate(9.784 -0.011)">
      <g id="Layer_2-2" data-name="Layer 2">
        <path id="Path_804" data-name="Path 804" class="cls-1" d="M93.605,32.61a9.044,9.044,0,0,1-2.631,4.731,67.3,67.3,0,0,0-5.484,5.55s1.9,11.017,14.548,9.3c0,0-4.08-14.91-.633-18.542C99.412,33.658,95.693,35.62,93.605,32.61Z" transform="translate(-37.677 -11.896)"/>
        <path id="Path_805" data-name="Path 805" class="cls-2" d="M103.084,34.852c-.177-.248-.367-.489-.563-.73a4,4,0,0,1-4.51-1.511,9.9,9.9,0,0,1-.531,1.7,13.352,13.352,0,0,0,4.984,6.128A14.035,14.035,0,0,1,103.084,34.852Z" transform="translate(-42.083 -11.896)"/>
        <path id="Path_806" data-name="Path 806" class="cls-3" d="M95.1,9.167a3.618,3.618,0,0,1-1.632,3.41c-1.493,1.086-3.422.883-4.643,2.6a24.161,24.161,0,0,0-1.8,4.147c-.848,1.861-2.144,3.524-3.163,5.283-3.7,6.191-18.027,19.05-24.195,8.852-3.352-5.544-2.53-13.481,2.094-16.18A17.171,17.171,0,0,1,71.915,15.46a1.787,1.787,0,0,1-.1-2.661c1.252-1.638,3.934.768,4.655-.451.468-.794.145-2.14.177-3.01A8.513,8.513,0,0,1,78.2,4.3c3.586-4.686,11.8-4.553,14.978.362A10.636,10.636,0,0,1,95.1,9.167Z" transform="translate(-27.435 -0.315)"/>
        <path id="Path_807" data-name="Path 807" class="cls-1" d="M109.072,15.53s-4.036,12.935-9.336,11.43-6.61-10.941-4.251-16.046S106.953,1.744,109.072,15.53Z" transform="translate(-40.935 -2.233)"/>
        <path id="Path_808" data-name="Path 808" class="cls-3" d="M109.835,12.714a8.338,8.338,0,0,0-5.282-7.957A11.815,11.815,0,0,0,99.993,3.71c-4.314-.044-7.369,2.934-7.534,7.62-.038,1.162.633,6.744,2.328,3.258.633-1.27,1.17-3.27,2.935-3.143,2.283.165,1.8,3.175,3.612,4.172a1.689,1.689,0,0,0,2.132-.114.9.9,0,0,0,.411-.337,2.476,2.476,0,0,0,4.194.9C109.569,15.6,109.848,14.136,109.835,12.714Z" transform="translate(-40.237 -1.347)"/>
        <path id="Path_809" data-name="Path 809" class="cls-4" d="M106.939,31.56s-3.314.521-5.149-1.27c0,0,.816,3.651,2.771,3.7S106.939,31.56,106.939,31.56Z" transform="translate(-43.666 -11.049)"/>
        <path id="Path_810" data-name="Path 810" class="cls-5" d="M58.965,122.411c-2.657-1.568-3.093-4.851-2.53-7.62.215-1.035.734-3.315,1.847-3.81,1.442-.635,1.265.432,1.992,1.638a16.025,16.025,0,0,0,2.132,2.965c.633.635,2.347,1.359,2.644,2.013,1.385,3.029-4.618,4.388-6.5,3.543Z" transform="translate(-26.92 -40.432)"/>
        <path id="Path_811" data-name="Path 811" class="cls-6" d="M152.647,36.42c-1.828,1.492-2.151,4.324-4.263,5.912s-4.055,1.613-2.448,4.445c1.48,2.616,5.94,6.928,8.856,7.62,1.012-2.324,1.025-4.82,2.353-7.169.525-.921,2.707-2.584,2.865-3.429.31-1.607-2.619-4.585-3.333-5.886Z" transform="translate(-59.67 -13.286)"/>
        <path id="Path_812" data-name="Path 812" class="cls-7" d="M112.064,94.95a10.081,10.081,0,0,0-2.4,11.589c3.068,6.915,11.386,12.306,19.931,17.043,6.414,3.569,8.33,6.1,5.5,10.979-3.549,6.128-7.59,4.864-12.372,10.9-1.651,2.324-6.091,9.341-6.091,9.341l3.947,2.54s-.24-1.518,4.592-4.921c5.307-3.734,14.858-8.509,17.781-10.719s7.862-5.036,5.649-12-21.785-24.028-21.785-24.028Z" transform="translate(-46.217 -34.65)"/>
        <path id="Path_813" data-name="Path 813" class="cls-8" d="M206.61,149.394c19.881-21.425,5.857-46.844,5.857-46.844l2.72.419s18.622,20.8.209,43.478Z" transform="translate(-82.184 -37.424)"/>
        <ellipse id="Ellipse_283" data-name="Ellipse 283" class="cls-8" cx="3.764" cy="3.778" rx="3.764" ry="3.778" transform="translate(126.115 63.139)"/>
        <ellipse id="Ellipse_284" data-name="Ellipse 284" class="cls-9" cx="17.996" cy="18.066" rx="17.996" ry="18.066" transform="translate(19.432 113.869)"/>
        <ellipse id="Ellipse_285" data-name="Ellipse 285" class="cls-10" cx="17.996" cy="18.066" rx="17.996" ry="18.066" transform="translate(19.432 113.869)"/>
        <ellipse id="Ellipse_286" data-name="Ellipse 286" class="cls-4" cx="9.298" cy="9.334" rx="9.298" ry="9.334" transform="translate(27.711 122.6)"/>
        <path id="Path_814" data-name="Path 814" class="cls-8" d="M89.422,129.947s-8.9,19.723.968,32.385,33.474,8.2,33.474,8.2l-.2,8.642L9.9,183.808s5.06-45.879,25.346-57.626S83.04,120.638,89.422,129.947Z" transform="translate(-9.9 -43.701)"/>
        <path id="Path_815" data-name="Path 815" class="cls-11" d="M80.313,168.033C77.024,154.8,59.439,152.7,40.647,162.991S11.43,193.643,11.43,193.643l.886.14,56.9-2.2C78.283,186.531,82.805,178.066,80.313,168.033Z" transform="translate(-10.462 -57.111)"/>
        <path id="Path_816" data-name="Path 816" class="cls-5" d="M80.313,168.033C77.024,154.8,59.439,152.7,40.647,162.991S11.43,193.643,11.43,193.643l.886.14,56.9-2.2C78.283,186.531,82.805,178.066,80.313,168.033Z" transform="translate(-10.462 -57.111)"/>
        <g id="Group_89" data-name="Group 89" class="cls-12" transform="translate(6.983 82.432)">
          <path id="Path_817" data-name="Path 817" class="cls-4" d="M21.478,154.966l-.538-.337c.171-.273,17.116-27.013,38.711-24.682,21.342,2.305,32.614,0,32.727,0l.127.635c-.114,0-11.474,2.318-32.892,0C38.385,128.3,21.648,154.693,21.478,154.966Z" transform="translate(-20.94 -129.803)"/>
        </g>
        <path id="Path_818" data-name="Path 818" class="cls-10" d="M104.736,99.25a11.977,11.977,0,0,0-.417,13.024c4.187,6.934,31.526,20.739,31.526,20.739s-8.419,11.627-12.126,19.05c-1.265,2.826-4.428,11.271-4.428,11.271l4.314,1.988s-.493-1.6,3.707-6.2c4.6-5.042,13.214-12,15.731-14.942s8.185-12.306,3.8-17.247-19.254-19.088-19.254-19.088Z" transform="translate(-43.949 -36.219)"/>
        <path id="Path_819" data-name="Path 819" class="cls-8" d="M252.028,127.308c0,5.277-1.777,9.525-3.979,9.525s-14.65-2.54-14.65-8.82,12.448-10.293,14.65-10.293S252.028,122.032,252.028,127.308Z" transform="translate(-92.028 -42.961)"/>
        <ellipse id="Ellipse_287" data-name="Ellipse 287" class="cls-4" cx="3.979" cy="9.557" rx="3.979" ry="9.557" transform="translate(152.043 74.791)"/>
        <path id="Path_820" data-name="Path 820" class="cls-2" d="M157.84,42.93a29.938,29.938,0,0,0,3.53,7.347c.108-.229.215-.464.342-.692a7.893,7.893,0,0,1,1.158-1.327C159.769,46.334,158.4,44.327,157.84,42.93Z" transform="translate(-64.263 -15.663)"/>
        <path id="Path_821" data-name="Path 821" class="cls-13" d="M130.571,47.287c-2.587.851-4.08,3.219-6.174,4.75-2.884,2.1-5.693,4.274-8.419,6.629-4.763,4.159-9.526,9.195-11.386,15.405C102.48,81.1,110.216,85.2,115.7,86.955c3.416,1.092,7.622,2.178,11.24,1.9,4.377-.362,3.365-4.223,3.865-7.62a23.006,23.006,0,0,1,3.011-8.465c2.119-3.81,4.472-7.62,5.693-11.824a9.973,9.973,0,0,0,.215-6.35c-.879-2.305-3.023-2.991-4.845-4.5-1-.825-2.6-3.308-4.055-2.9Z" transform="translate(-44.565 -17.206)"/>
        <path id="Path_822" data-name="Path 822" class="cls-8" d="M150.751,58.766c-2.423,2.318-1.815,9.658.164,16.51,1.948-3.454,3.979-6.921,5.1-10.738a9.973,9.973,0,0,0,.215-6.35,4.867,4.867,0,0,0-.443-.883C153.75,56.632,152.44,57.153,150.751,58.766Z" transform="translate(-61.077 -20.805)"/>
        <path id="Path_823" data-name="Path 823" class="cls-6" d="M191.088,96.047a3.372,3.372,0,0,0-.886,2.14c-.753,7.029,12.758-1.461,16.275,1.562,2.435,2.1,1.392-3.143-1.9-4.712C202.391,93.983,192.992,94.142,191.088,96.047Z" transform="translate(-76.144 -34.445)"/>
        <ellipse id="Ellipse_288" data-name="Ellipse 288" class="cls-9" cx="17.996" cy="18.066" rx="17.996" ry="18.066" transform="translate(116.684 112.808)"/>
        <ellipse id="Ellipse_289" data-name="Ellipse 289" class="cls-10" cx="17.996" cy="18.066" rx="17.996" ry="18.066" transform="translate(116.684 112.808)"/>
        <ellipse id="Ellipse_290" data-name="Ellipse 290" class="cls-4" cx="9.292" cy="9.328" rx="9.292" ry="9.328" transform="translate(125.388 121.552)"/>
        <path id="Path_824" data-name="Path 824" class="cls-13" d="M181.375,80.66a109.645,109.645,0,0,0-13.555,3.277c-.886-4.1-2.423-7.957-2.846-12.173-.373-3.708-.841-7.277-3.321-10.382-1.9-2.407-5.4-4.82-8.261-2.692-5.06,3.753-1.537,14.287,0,19.094,1.265,3.886,3.605,12.541,8.223,13.875,6.218,1.8,12.347-2.286,21.082-4.553Z" transform="translate(-61.661 -21.115)"/>
        <path id="Path_825" data-name="Path 825" class="cls-8" d="M227.939,176.392a22.718,22.718,0,0,0-38.768,22.86c5.522.286,13.049-1.588,20.6-5.493C219.394,188.806,226.307,181.929,227.939,176.392Z" transform="translate(-75.144 -61.228)"/>
        <ellipse id="Ellipse_291" data-name="Ellipse 291" class="cls-14" cx="5.547" cy="5.569" rx="5.547" ry="5.569" transform="translate(133.756 108.744)"/>
        <ellipse id="Ellipse_292" data-name="Ellipse 292" class="cls-14" cx="3.789" cy="3.804" rx="3.789" ry="3.804" transform="translate(44.689 107.258)"/>
        <ellipse id="Ellipse_293" data-name="Ellipse 293" class="cls-14" cx="1.562" cy="1.568" rx="1.562" ry="1.568" transform="translate(52.374 104.934)"/>
        <path id="Path_826" data-name="Path 826" class="cls-6" d="M162.937,17.663c3.239.273,6.743,4.8,7.224,7.944a26.985,26.985,0,0,0,2.347,6.534l-2.315,1.13s-1.619,6.794-6.268,6.566c-2.941-.14-7.072-3.353-8.634-6.833-2.17-4.826-1.006-6.35.076-9.309.968-2.642,4.2-5.715,6.92-6.026a3.566,3.566,0,0,1,.651-.006Z" transform="translate(-62.876 -6.436)"/>
        <ellipse id="Ellipse_294" data-name="Ellipse 294" class="cls-14" cx="2.41" cy="2.419" rx="2.41" ry="2.419" transform="translate(140.227 109.29)"/>
        <path id="Path_827" data-name="Path 827" class="cls-3" d="M163.31,24.245c1.265-.248,2.334.635,3.53.927a7.021,7.021,0,0,0,4.371-.489c.86-.406,2.233-1.537,1.8-2.642a1.932,1.932,0,0,0-2.53-.73c1.986-2.045-.051-4.445-2.176-5.188a21.25,21.25,0,0,0-3.839-.749c-2.41-.387-5.364,1.111-7.413,2.2a7.5,7.5,0,0,0-3.435,4.445c-.765,2.1-1.543,6,.089,7.861.576.635,6.724,1.905,6.559.756-.19-1.27-1.455-2.54-.829-3.854a1.488,1.488,0,0,1,2.606,0c.67-.387.8-1.638,1.354-2.286Z" transform="translate(-62.389 -5.582)"/>
        <path id="Path_828" data-name="Path 828" class="cls-4" d="M130.524,192.135l-2.834,4.686s3.966,5.41,7.831,6.052-3.909-4.1-.525-7.582S130.524,192.135,130.524,192.135Z" transform="translate(-53.184 -70.12)"/>
        <path id="Path_829" data-name="Path 829" class="cls-10" d="M64.8,96.78A11.971,11.971,0,0,0,63.075,109.7c3.473,7.322,32.759,20.091,32.974,22.225S85.53,138.569,80.982,147.84a105.281,105.281,0,0,1-8.134,14.2l4.093,2.413s-.329-1.645,4.308-5.791c5.06-4.553,20.7-18.066,23.233-22.085s-3.163-15.24-9.912-20.32-7.945-8.611-7.945-8.611Z" transform="translate(-29 -35.318)"/>
        <path id="Path_830" data-name="Path 830" class="cls-4" d="M81.223,191.83l-3.283,4.375s3.409,5.778,7.186,6.814-3.479-4.445.24-7.62S81.223,191.83,81.223,191.83Z" transform="translate(-34.902 -70.011)"/>
        <path id="Path_831" data-name="Path 831" class="cls-14" d="M47.9,194.2c0,.362-.2.635-.43.635H30.55c-.24,0-.43-.292-.43-.635s.2-.635.43-.635H47.464c.24,0,.43.292.43.635Z" transform="translate(-17.33 -70.646)"/>
        <path id="Path_832" data-name="Path 832" class="cls-14" d="M163.361,208.765c0,.362-.531.635-1.183.635H116c-.633,0-1.183-.292-1.183-.635s.531-.635,1.183-.635h46.175C162.811,208.13,163.361,208.422,163.361,208.765Z" transform="translate(-48.454 -75.961)"/>
        <path id="Path_833" data-name="Path 833" class="cls-14" d="M59.174,200.865a.634.634,0,0,1-.6.635H35.113a.635.635,0,0,1,0-1.27H58.573A.634.634,0,0,1,59.174,200.865Z" transform="translate(-18.932 -73.077)"/>
        <path id="Path_834" data-name="Path 834" class="cls-8" d="M169.424,18.065c-.588.4-.424.419-1.9.559-2.391.235-6.7,1.162-8.008,6.166-.183.7.386,1.448-.791,2.248a7.382,7.382,0,0,1-3.416.781h-3.669s-9.064-9.9,1.746-16.123c7.331-4.223,13.321,1.041,15.288,4.229C169.614,17.43,170.012,17.664,169.424,18.065Z" transform="translate(-60.679 -3.688)"/>
        <path id="Path_970" data-name="Path 970" class="cls-4" d="M.907,0c.5,0,.905.7.9,1.557S1.4,3.114.9,3.115,0,2.419,0,1.558.406,0,.907,0Z" transform="translate(102.403 9.701) rotate(-51.25)"/>
        <path id="Path_835" data-name="Path 835" class="cls-4" d="M163.261,20.977a9.751,9.751,0,0,0-4.01,6.248c-.323,1.632-3.53,2.489-7.59,2.089l.24.356a17.334,17.334,0,0,0,3.5,0c2.34-.279,3.934-1.118,4.181-2.356a9.306,9.306,0,0,1,3.858-6.039c1.9-1.4,5.092-1.9,6.6-2.045l-.044-.07-.158-.241C168.245,19.06,165.158,19.568,163.261,20.977Z" transform="translate(-61.992 -6.899)"/>
        <path id="Path_836" data-name="Path 836" class="cls-15" d="M108.316,9.457a9.329,9.329,0,0,1,0,1.41c-.108,1.791-.569,3.626-2.195,4.674-.715-2.337-3.39-3.334-5.63-3.988-2.068-.6-4-1.27-5.737.279-1.221,1.1-2.321,2.476-4.149,2.235-1.632-.21-2.442-1.435-2.745-2.991a10.835,10.835,0,0,1,.506-5.563A8.663,8.663,0,0,1,93.286.631C99.466-1.706,107.7,2.587,108.316,9.457Z" transform="translate(-38.49 0.011)"/>
        <path id="Path_971" data-name="Path 971" class="cls-4" d="M1.388,0A1.372,1.372,0,0,1,2.773,1.364a1.385,1.385,0,0,1-1.39,1.372A1.372,1.372,0,0,1,0,1.369,1.385,1.385,0,0,1,1.388,0Z" transform="matrix(0.741, -0.672, 0.672, 0.741, 61.68, 9.339)"/>
        <path id="Path_837" data-name="Path 837" class="cls-15" d="M77.621,44.289c4.984-.127,9.558,9.76,11.3,12.5s2.834,5.34.215,8.6-5.427,4-6.186,7.766c-.183.9-.291,14.884.354,14.865-3.466.108-6.718-1.613-10.121-1.968-3.055-.324-6.382-.222-9.216-1.651a27.437,27.437,0,0,1-8.179-6.2c-.8-.908-2.359-2.464-2.366-3.721,0-1.772,2.03-2.057,3.409-2.54,4.257-1.429,7.464-3.7,8.887-8.147C68.177,56.107,72.643,44.423,77.621,44.289Z" transform="translate(-25.892 -16.158)"/>
        <path id="Path_838" data-name="Path 838" class="cls-1" d="M85.918,52.021c-1.449,3.372.354,15.824,6.99,22.669s11.727,5.759,20.76,4.489c2.771-.394,5.693-1.27,8.419-.883,1.645.248,4.428.635,3.605-2.362-.436-1.657-2.846-2.33-4.352-2.5-4.124-.457-8.476,1.632-12.651,1.683-7.483.089-8.495-4.407-10.178-9.868-1.955-7.791-2.492-11.709-4.055-13.97S87.367,48.649,85.918,52.021Z" transform="translate(-37.662 -18.073)"/>
      </g>
    </g>
  </g>
</svg>
