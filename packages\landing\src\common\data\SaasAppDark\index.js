// menu items
export const menu_items = [
  {
    label: 'Home',
    path: '#home',
    offset: '70',
  },
  {
    label: 'How to',
    path: '#how-to',
    offset: '70',
  },
  {
    label: 'Testimonials',
    path: '#testimonials',
    offset: '70',
  },
  {
    label: 'Newsfeed',
    path: '#newsfeed',
    offset: '70',
  },
  {
    label: 'Download App',
    path: '#download_app',
    offset: '70',
  },
];

// service section
import envato from 'common/assets/image/saasAppDark/clients/envato.png';
import evernote from 'common/assets/image/saasAppDark/clients/evernote.png';
import forbes from 'common/assets/image/saasAppDark/clients/forbes.png';
import geekwire from 'common/assets/image/saasAppDark/clients/geekwire.png';
import slack from 'common/assets/image/saasAppDark/clients/slack.png';
import usaToday from 'common/assets/image/saasAppDark/clients/usa-today.png';
import headphone from 'common/assets/image/saasAppDark/icons/headphone.svg';
// monitoring section
import rocket from 'common/assets/image/saasAppDark/icons/rocket.svg';
// service section
import service1 from 'common/assets/image/saasAppDark/icons/service1.svg';
import service2 from 'common/assets/image/saasAppDark/icons/service2.svg';
import service3 from 'common/assets/image/saasAppDark/icons/service3.svg';
import service4 from 'common/assets/image/saasAppDark/icons/service4.svg';
import service5 from 'common/assets/image/saasAppDark/icons/service5.svg';
// footer section
import siteLogo from 'common/assets/image/saasAppDark/logo.svg';
// news feed section
import post1 from 'common/assets/image/saasAppDark/post1.png';
import post2 from 'common/assets/image/saasAppDark/post2.png';
import post3 from 'common/assets/image/saasAppDark/post3.png';
// testimonials section
import logo1 from 'common/assets/image/saasAppDark/tm-logo1.svg';
import logo2 from 'common/assets/image/saasAppDark/tm-logo2.svg';
import logo3 from 'common/assets/image/saasAppDark/tm-logo3.svg';
export const clients = [envato, evernote, forbes, geekwire, slack, usaToday];


export const services = [
  {
    id: 1,
    title: 'Fast Performance',
    icon: service1,
  },
  {
    id: 2,
    title: 'User Customization',
    icon: service2,
  },
  {
    id: 3,
    title: 'Modify structure',
    icon: service3,
  },
  {
    id: 4,
    title: 'Customer Analysis',
    icon: service4,
  },
  {
    id: 5,
    title: 'Instant Support',
    icon: service5,
  },
];


export const monitoringFeatures = [
  {
    id: 1,
    icon: rocket,
    title: 'Daily Graph analysis',
    desc: `Stay on top of your task lists and stay in touch with what's happening by the latest UI updates`,
  },
  {
    id: 2,
    icon: headphone,
    title: 'Communication Speedy',
    desc: `Stay on top of your task lists and stay in touch with what's happening and what’s out.`,
  },
];

// stats counter section
export const statsCounter = {
  blockTitle: {
    subtitle: 'Monthly Stats',
    title: `Take your user monitoring experience to new ultimate level with file tracking`,
    text: 'Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool. It is built to perform and run fast on all of the latest mobile devices. Build out-of the box blazing fast apps with a small footprint and built-in best practices.',
    button: {
      link: '#',
      label: 'Discover more',
    },
  },
  posts: [
    {
      count: '80',
      title: 'Up to',
      text: 'Customer Response',
      symbol: '%',
    },
    {
      count: '99',
      title: 'Consistent',
      text: 'Performance Score',
      symbol: '%',
    },
    {
      count: '3.5',
      title: 'Down to',
      text: 'Response Time',
      symbol: 'S',
    },
    {
      count: '5x',
      title: 'Up to',
      text: 'Faster then others',
      symbol: '',
    },
  ],
};

// video intro section
export const videoIntro = {
  features: [
    {
      id: 1,
      title: '1-month Free Trial',
      desc: 'We are open for trial sessions',
    },
    {
      id: 2,
      title: 'Sales Widgets',
      desc: 'Stay on top of your task lists',
    },
    {
      id: 3,
      title: 'Free security update',
      desc: 'Every month we provide updates',
    },
  ],
};

export const posts = [
  {
    id: 1,
    date: 'June 3, 2020',
    image: post1,
    title: 'The three Fundamental Rules to Keep Your Website Goal Orientated',
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
  {
    id: 2,
    date: 'Dec 8, 2020',
    image: post2,
    title: 'Five Common Mistakes Teams Make When Tracking Performance',
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
  {
    id: 3,
    date: 'Dec 8, 2020',
    image: post3,
    title: `Why You Might Want to Reconsider with Tracking First Meaningful Paint`,
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
];

export const testimonials = [
  {
    id: 1,
    logo: logo1,
    author: 'Johnny Simpson',
    designation: 'Head of Design',
    quote: `Each room is loaded with the most collaborative surfaces so you can quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with the editors.`,
  },
  {
    id: 2,
    logo: logo2,
    author: 'Deanna Hodges',
    designation: 'Business HR Admin',
    quote: `We deliver on such an expansive with innovation agenda with so many theme projects going on at any time, it can be hard to maintain momentum. So We appreciate to work with them.`,
  },
  {
    id: 3,
    logo: logo3,
    author: 'Gracelyn Mason',
    designation: 'Senior Marketer',
    quote: `Flat item is loaded with the most of collaborative surfaces so you can do quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with statics.`,
  },
  {
    id: 4,
    logo: logo1,
    author: 'Gracelyn Mason',
    designation: 'Senior Marketer',
    quote: `Flat item is loaded with the most of collaborative surfaces so you can do quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with statics.`,
  },
];

export const footer = {
  about: {
    logo: siteLogo,
    text: `We run Advanced Search reports on the criteria you care about to see how work is progressing and where to focus your effort.`,
  },
  widgets: [
    {
      id: 2,
      title: 'About Us',
      list: [
        {
          id: 1,
          title: 'Support Center',
          link: '#',
        },
        {
          id: 2,
          title: 'Customer Support',
          link: '#',
        },
        {
          id: 3,
          title: 'About Us',
          link: '#',
        },
        {
          id: 4,
          title: 'Copyright',
          link: '#',
        },
        {
          id: 5,
          title: 'Popular Campaign',
          link: '#',
        },
      ],
    },
    {
      id: 3,
      title: 'Our Information',
      list: [
        {
          id: 1,
          title: 'Return Policy ',
          link: '#',
        },
        {
          id: 2,
          title: 'Privacy Policy',
          link: '#',
        },
        {
          id: 3,
          title: 'Terms & Conditions',
          link: '#',
        },
        {
          id: 4,
          title: 'Site Map',
          link: '#',
        },
        {
          id: 5,
          title: 'Store Hours',
          link: '#',
        },
      ],
    },
    {
      id: 4,
      title: 'My Account',
      list: [
        {
          id: 1,
          title: 'Press inquiries',
          link: '#',
        },
        {
          id: 2,
          title: 'Social media ',
          link: '#',
        },
        {
          id: 3,
          title: 'directories',
          link: '#',
        },
        {
          id: 4,
          title: 'Images & B-roll',
          link: '#',
        },
        {
          id: 5,
          title: 'Permissions',
          link: '#',
        },
      ],
    },
  ],
  contactInfo: {
    title: 'Contact info',
    address: `Amsterdam, Netherlands`,
    phone: `+31 62 19 22 705`,
    openingTime: `7 Days - 8am - 10pm`,
    email: `<EMAIL>`,
  },
};
