<svg xmlns="http://www.w3.org/2000/svg" width="160" height="150" viewBox="0 0 160 150">
  <defs>
    <style>
      .cls-1 {
        fill: #596a83;
      }

      .cls-2 {
        fill: #6749b5;
      }

      .cls-3 {
        fill: #9371ec;
      }

      .cls-4 {
        fill: #dde4ea;
      }

      .cls-5 {
        fill: #2e3054;
      }

      .cls-12, .cls-14, .cls-6 {
        fill: #fff;
      }

      .cls-7 {
        fill: #7859c9;
      }

      .cls-12, .cls-8 {
        opacity: 0.4;
      }

      .cls-9 {
        fill: #ecb49f;
      }

      .cls-10 {
        fill: #bc428b;
      }

      .cls-11 {
        fill: #b1317d;
      }

      .cls-13 {
        fill: #acd0ef;
      }

      .cls-14 {
        opacity: 0.5;
      }

      .cls-15 {
        fill: #ff92a7;
      }

      .cls-16 {
        fill: #727f9c;
      }
    </style>
  </defs>
  <g id="service_5" data-name="service 5" transform="translate(-77.28 -49.518)">
    <g id="Layer_2" data-name="Layer 2" transform="translate(77.28 49.518)">
      <g id="Layer_2-2" data-name="Layer 2">
        <path id="Path_901" data-name="Path 901" class="cls-1" d="M162.548,110.41a11.3,11.3,0,0,0-2.277,12.179c2.916,7.261,10.848,12.935,18.932,17.909,6.092,3.733,7.916,6.4,5.227,11.544-3.361,6.439-7.21,5.114-11.764,11.451-1.571,2.436-5.79,9.818-5.79,9.818l3.756,2.641s-.227-1.6,4.37-5.17c5.042-3.929,14.117-8.94,16.89-11.264s7.47-5.3,5.369-12.617-20.7-25.253-20.7-25.253Z" transform="translate(-90.392 -53.584)"/>
        <path id="Path_902" data-name="Path 902" class="cls-2" d="M229.39,165.087c18.89-22.519,5.571-49.237,5.571-49.237l2.588.439s17.688,21.856.193,45.728Z" transform="translate(-101.573 -53.947)"/>
        <ellipse id="Ellipse_298" data-name="Ellipse 298" class="cls-3" cx="3.58" cy="3.976" rx="3.58" ry="3.976" transform="translate(127.615 59.477)"/>
        <ellipse id="Ellipse_299" data-name="Ellipse 299" class="cls-4" cx="17.1" cy="18.991" rx="17.1" ry="18.991" transform="translate(22.444 112.017)"/>
        <ellipse id="Ellipse_300" data-name="Ellipse 300" class="cls-5" cx="17.1" cy="18.991" rx="17.1" ry="18.991" transform="translate(22.444 112.017)"/>
        <ellipse id="Ellipse_301" data-name="Ellipse 301" class="cls-6" cx="8.831" cy="9.808" rx="8.831" ry="9.808" transform="translate(30.318 121.2)"/>
        <path id="Path_903" data-name="Path 903" class="cls-2" d="M159.912,138.873s-9.142,20.736.992,34.016,34.393,8.623,34.393,8.623l-.2,9.09L78.21,195.483s5.168-48.22,26.049-60.567S153.357,129.093,159.912,138.873Z" transform="translate(-77.429 -54.769)"/>
        <path id="Path_904" data-name="Path 904" class="cls-7" d="M150.169,166.562c-3.361-13.905-21.469-16.117-40.754-5.3S79.4,193.495,79.4,193.495l.908.149,58.417-2.286C148.085,186,152.732,177.107,150.169,166.562Z" transform="translate(-77.619 -56.523)"/>
        <g id="Group_90" data-name="Group 90" class="cls-8" transform="translate(8.092 78.942)">
          <path id="Path_905" data-name="Path 905" class="cls-6" d="M87.2,161.65l-.286-.224A82.87,82.87,0,0,1,98.447,147.66c6.546-6.271,16.806-13.69,28.385-13.551,23.2.317,33.326,1.428,33.427,1.447v.392c-.1,0-10.218-1.129-33.4-1.437h-.378C104.632,134.5,87.372,161.37,87.2,161.65Z" transform="translate(-86.91 -134.108)"/>
        </g>
        <path id="Path_906" data-name="Path 906" class="cls-5" d="M157.074,113.49a13.723,13.723,0,0,0-.395,13.691c3.975,7.289,16.3,8.828,21.848,13.905s8.285,6.953,6.42,13.065c-2.344,7.69-6.336,7.037-9.848,14.848-1.176,2.968-4.2,11.852-4.2,11.852l4.1,2.09s-.462-1.689,3.521-6.533c4.37-5.3,12.554-12.608,14.94-15.706s6.563-7.289,3.361-14.792-18.091-23.405-18.091-23.405Z" transform="translate(-89.699 -53.789)"/>
        <path id="Path_907" data-name="Path 907" class="cls-7" d="M263.722,136.782c0,5.553-1.681,10.042-3.773,10.042S246,144.182,246,137.556s11.831-10.816,13.915-10.816S263.722,131.238,263.722,136.782Z" transform="translate(-104.226 -54.674)"/>
        <ellipse id="Ellipse_302" data-name="Ellipse 302" class="cls-3" cx="3.773" cy="10.042" rx="3.773" ry="10.042" transform="translate(151.95 72.066)"/>
        <path id="Path_908" data-name="Path 908" class="cls-9" d="M199.423,77.319s12.327-8.4,7.159-16.8-12.722-8.4-16.7,2.65-2.664,10.163-10.218,14.577-20,11.917-23.974,22.071,10.739,17.666,21.469,18.991c0,0,6.722-2.65,8.352-8.828S193.851,82.172,199.423,77.319Z" transform="translate(-89.697 -49.852)"/>
        <path id="Path_909" data-name="Path 909" class="cls-10" d="M185.2,75.283c-1.26-1.12-2.924,2.1-5.983,3.864-7.68,4.442-19.8,11.992-23.839,22.211s10.924,17.731,21.848,19.094c0,0,6.874-2.669,8.487-8.884,1.487-5.73,7.437-24.656,12.789-31.413C198.959,79.576,188.136,77.869,185.2,75.283Z" transform="translate(-89.646 -51.223)"/>
        <path id="Path_911" data-name="Path 911" class="cls-11" d="M200.727,98.745c-.941-7.167.7-11-3.7-13.84-3.521-2.277-5.042-2.081-7.21.224-2.924,3.1-.908,15.221,2.227,23.517C194.786,102.151,200.164,98.679,200.727,98.745Z" transform="translate(-95.027 -51.773)"/>
        <ellipse id="Ellipse_303" data-name="Ellipse 303" class="cls-4" cx="17.1" cy="18.991" rx="17.1" ry="18.991" transform="translate(120.456 112.017)"/>
        <ellipse id="Ellipse_305" data-name="Ellipse 305" class="cls-5" cx="17.1" cy="18.991" rx="17.1" ry="18.991" transform="translate(120.456 112.017)"/>
        <ellipse id="Ellipse_306" data-name="Ellipse 306" class="cls-6" cx="8.831" cy="9.808" rx="8.831" ry="9.808" transform="translate(128.724 121.2)"/>
        <path id="Path_912" data-name="Path 912" class="cls-9" d="M190.835,84.236c-4.613,4.2,1.513,21.072,8.268,28.137s11.4,5.31,19.7,2.93c6.554-1.866,8.336-3.434,11.125-.784s1.588-3.976-2.185-5.963-6.958,1.764-13.915,2.65-8.277-4.116-10.31-10.135c-2.521-8.632-.118-14.064-5.47-17.031C194.532,82.043,193.02,82.248,190.835,84.236Z" transform="translate(-95.159 -51.73)"/>
        <path id="Path_913" data-name="Path 913" class="cls-2" d="M254.145,171.56c-6.683-9.379-18.571-11.846-27.853-5.78s-13.113,18.805-8.977,29.849c5.252.3,12.4-1.67,19.57-5.777C246.028,184.606,252.6,177.383,254.145,171.56Z" transform="translate(-99.38 -57.06)"/>
        <ellipse id="Ellipse_307" data-name="Ellipse 307" class="cls-12" cx="5.269" cy="5.851" rx="5.269" ry="5.851" transform="translate(136.682 107.753)"/>
        <ellipse id="Ellipse_308" data-name="Ellipse 308" class="cls-12" cx="2.815" cy="7.513" rx="2.815" ry="7.513" transform="translate(153.168 73.26)"/>
        <ellipse id="Ellipse_309" data-name="Ellipse 309" class="cls-12" cx="2.588" cy="3.518" rx="2.588" ry="3.518" transform="translate(154.824 73.83)"/>
        <ellipse id="Ellipse_310" data-name="Ellipse 310" class="cls-12" cx="3.596" cy="3.994" rx="3.596" ry="3.994" transform="translate(52.056 106.185)"/>
        <ellipse id="Ellipse_311" data-name="Ellipse 311" class="cls-12" cx="1.487" cy="1.652" rx="1.487" ry="1.652" transform="translate(59.35 103.74)"/>
        <ellipse id="Ellipse_312" data-name="Ellipse 312" class="cls-12" cx="2.294" cy="2.548" rx="2.294" ry="2.548" transform="translate(142.824 108.322)"/>
        <path id="Path_915" data-name="Path 915" class="cls-6" d="M175.629,179.929l-2.689,4.927s3.773,5.683,7.445,6.365-3.714-4.3-.5-7.97S175.629,179.929,175.629,179.929Z" transform="translate(-92.558 -58.225)"/>
        <path id="Path_916" data-name="Path 916" class="cls-10" d="M202.584,96.742c-.941-6.159-.143-10.266-4.529-12.7-3.521-1.95-5.042-1.792-7.21.2-2.924,2.66-1.529,10.424,1.6,17.545C195.156,96.21,202.013,96.7,202.584,96.742Z" transform="translate(-95.161 -51.731)"/>
        <path id="Path_917" data-name="Path 917" class="cls-12" d="M128.834,181.14a.712.712,0,0,1-.748.681H98.676a.6.6,0,0,1-.649-.3.765.765,0,0,1,0-.779.6.6,0,0,1,.649-.3h29.41a.721.721,0,0,1,.748.681Z" transform="translate(-80.576 -58.259)"/>
        <path id="Path_918" data-name="Path 918" class="cls-12" d="M209.844,192.031c0,.373-.5.681-1.126.681h-43.93c-.622,0-1.118-.308-1.118-.681s.5-.681,1.118-.681h43.9c.622,0,1.126.308,1.126.681Z" transform="translate(-91.077 -58.988)"/>
        <path id="Path_919" data-name="Path 919" class="cls-12" d="M117.444,186.371a.65.65,0,0,1-.613.681H92.723a.69.69,0,0,1,0-1.372h24.1a.65.65,0,0,1,.613.681Z" transform="translate(-79.648 -58.609)"/>
        <path id="Path_920" data-name="Path 920" class="cls-13" d="M160.09,111.769a26.781,26.781,0,0,1-4.588-8.539c-.076.205-.143.383-.2.569a27.3,27.3,0,0,0,4.512,8.25c3.269,4.05,9.184,8.688,19.142,8.688H180.3l.639-.457C169.921,120.9,163.526,116.015,160.09,111.769Z" transform="translate(-89.74 -53.104)"/>
        <path id="Path_921" data-name="Path 921" class="cls-14" d="M101.511,95.94v2.4L87.47,96.9Z" transform="translate(-78.907 -52.618)"/>
        <path id="Path_922" data-name="Path 922" class="cls-14" d="M96.515,108.77V110.9L84.07,109.619Z" transform="translate(-78.364 -53.474)"/>
        <path id="Path_923" data-name="Path 923" class="cls-14" d="M98.514,103.17v2.4L77.28,104.131Z" transform="translate(-77.28 -53.1)"/>
        <path id="Path_924" data-name="Path 924" class="cls-5" d="M216.071,61.2s3.849.625,1.84,3.36-6-2.6-9.823-1.1S209.676,60.712,216.071,61.2Z" transform="translate(-97.968 -50.295)"/>
        <path id="Path_925" data-name="Path 925" class="cls-5" d="M201.089,70.37s-2.521,2.277-7.479,1.493l.176-3.733,6.722,1.96Z" transform="translate(-95.859 -50.761)"/>
        <path id="Path_926" data-name="Path 926" class="cls-10" d="M213.462,60.42c-.681.392-.5.429-2.084.4-2.58-.056-7.285.439-9.31,6.159-.286.812.227,1.764-1.126,2.538a7.272,7.272,0,0,1-3.739.448l-3.916-.513s-8.4-12.935,3.874-18.767c8.336-3.957,14.066,3.08,15.764,7.111C213.739,59.7,214.134,60.028,213.462,60.42Z" transform="translate(-95.359 -49.518)"/>
        <path id="Path_969" data-name="Path 969" class="cls-6" d="M1.017-.054c.563-.03.978.737.926,1.713S1.4,3.448.833,3.478s-.978-.737-.926-1.713S.455-.025,1.017-.054Z" transform="translate(111.911 4.483) rotate(-44.56)"/>
        <path id="Path_927" data-name="Path 927" class="cls-6" d="M205.824,59.6a11.373,11.373,0,0,0-5.042,6.8c-.546,1.866-4.075,2.445-8.352,1.409l.21.448a16.8,16.8,0,0,0,3.731.467c2.521,0,4.336-.765,4.748-2.2a10.863,10.863,0,0,1,4.865-6.589c2.227-1.381,5.664-1.512,7.285-1.493v-.093l-.134-.308C211.4,58,208.034,58.2,205.824,59.6Z" transform="translate(-95.67 -50.087)"/>
        <rect id="Rectangle_77" data-name="Rectangle 77" class="cls-15" width="38.569" height="35.341" transform="translate(25.444 39.449)"/>
        <rect id="Rectangle_78" data-name="Rectangle 78" class="cls-14" width="38.569" height="4.536" transform="translate(25.444 39.449)"/>
        <path id="Path_928" data-name="Path 928" class="cls-1" d="M130.3,118.32s1.076,6.682,4.731,6.925c0,0-1.437,1.111-5.664,1.036s-5.3-1.036-5.3-1.036c3.655-.243,4.731-6.925,4.731-6.925Z" transform="translate(-84.753 -54.112)"/>
        <path id="Path_929" data-name="Path 929" class="cls-16" d="M142.4,116.206c0,1.344-6.529,2.93-14.579,2.93s-14.571-1.586-14.571-2.93,6.521-2.436,14.571-2.436S142.4,114.862,142.4,116.206Z" transform="translate(-83.025 -53.808)"/>
        <path id="Path_930" data-name="Path 930" class="cls-1" d="M140.67,114.116c0-7.52-5.489-13.616-12.26-13.616s-12.26,6.1-12.26,13.616c.092,1.167,5.546,2.081,12.26,2.081s12.167-.933,12.26-2.053Z" transform="translate(-83.488 -52.922)"/>
        <ellipse id="Ellipse_314" data-name="Ellipse 314" class="cls-1" cx="1.386" cy="1.54" rx="1.386" ry="1.54" transform="translate(43.804 44.965)"/>
        <path id="Path_931" data-name="Path 931" class="cls-14" d="M127.9,102s-4.907,3.089-5.042,12.711a34.991,34.991,0,0,1-3.924-.541C119.064,110.147,120.216,103.251,127.9,102Z" transform="translate(-83.932 -53.022)"/>
        <path id="Path_932" data-name="Path 932" class="cls-10" d="M129.677,91.013s-.353-.177-.95-.523a21.354,21.354,0,0,1-2.37-1.614,16.375,16.375,0,0,1-1.487-1.288,11.031,11.031,0,0,1-1.5-1.764,6.855,6.855,0,0,1-.639-1.176,4.165,4.165,0,0,1-.336-1.54,3.216,3.216,0,0,1,.429-1.7,3.554,3.554,0,0,1,.479-.663,2.713,2.713,0,0,1,.269-.261l.143-.121.067-.065.084-.065.277-.177.126-.084.193-.093a3.58,3.58,0,0,1,.387-.168l.395-.093a2.393,2.393,0,0,1,1.6.327A3.556,3.556,0,0,1,127.946,81a7.407,7.407,0,0,1,.655,1.148,13.1,13.1,0,0,1,.781,2.24,20.787,20.787,0,0,1,.429,2.034,27.959,27.959,0,0,1,.344,3.061v1.167a.367.367,0,0,1-.336.392.3.3,0,0,1-.143,0Zm-.37-1.241v-.14c-.16-.7-.395-1.7-.723-2.874-.16-.588-.353-1.213-.571-1.866a14.672,14.672,0,0,0-.84-1.866,5.5,5.5,0,0,0-.521-.831,2.278,2.278,0,0,0-.588-.551.814.814,0,0,0-.546-.1h-.151l-.134.065h-.067l-.118.075-.244.159h0l-.059.047-.084.075a1.126,1.126,0,0,0-.16.14,1.6,1.6,0,0,0-.227.308,1.139,1.139,0,0,0-.176.653,3.6,3.6,0,0,0,.6,1.708,12.69,12.69,0,0,0,1.193,1.615c.429.495.84.933,1.269,1.353.84.812,1.546,1.465,2.05,1.913Z" transform="translate(-84.486 -51.526)"/>
        <path id="Path_933" data-name="Path 933" class="cls-10" d="M131.56,90.667s.092-.392.3-1.036a27.09,27.09,0,0,1,1-2.65,18.345,18.345,0,0,1,.84-1.708,14.38,14.38,0,0,1,1.134-1.82,9.865,9.865,0,0,1,.748-.933,4.743,4.743,0,0,1,.983-.812,2.679,2.679,0,0,1,1.42-.448,2.3,2.3,0,0,1,.84.168,1.605,1.605,0,0,1,.37.187l.176.112.109.084.076.075.1.093.05.047.084.1a2.4,2.4,0,0,1,.319.485,2.361,2.361,0,0,1,.227,1.055,3.3,3.3,0,0,1-.5,1.577,6.169,6.169,0,0,1-.748,1.027,11.1,11.1,0,0,1-.79.812,16.079,16.079,0,0,1-1.588,1.3c-.513.383-1.017.709-1.479,1a24.694,24.694,0,0,1-2.3,1.288c-.571.28-.908.42-.908.42a.313.313,0,0,1-.267-.013.372.372,0,0,1-.179-.221.433.433,0,0,1,0-.233Zm.143-.177.773-.635c.487-.4,1.185-.933,2.017-1.661.412-.345.84-.737,1.3-1.139s.916-.859,1.361-1.325a7.48,7.48,0,0,0,1.143-1.493,1.307,1.307,0,0,0,.193-.569.146.146,0,0,0,0-.056h0l-.05-.056-.1-.093-.05-.047h-.025a.252.252,0,0,0-.1-.056.6.6,0,0,0-.2-.047,1.117,1.117,0,0,0-.555.187,5.415,5.415,0,0,0-1.277,1.195c-.4.495-.79,1.017-1.134,1.531s-.664,1.017-.95,1.493c-.571.933-1.034,1.764-1.361,2.333l-.5.933Z" transform="translate(-85.949 -51.638)"/>
        <path id="Path_934" data-name="Path 934" class="cls-10" d="M120.277,92.656c-2.731,0-5.235-1.605-7.563-4.825-4.2-5.842-8.537-5.683-15.125-4.731a.674.674,0,0,1-.735-.63.718.718,0,0,1,.567-.817c6.865-.989,11.705-1.157,16.344,5.263,3.361,4.666,6.916,5.459,11.268,2.613a.6.6,0,0,1,.683-.045.767.767,0,0,1-.019,1.3A9.948,9.948,0,0,1,120.277,92.656Z" transform="translate(-80.405 -51.63)"/>
      </g>
    </g>
  </g>
</svg>
