const data = {
  testimonials: [
    {
      id: 1,
      description:
        'Best working experience  with this amazing team & in future, we want to work together',
      name: '<PERSON>',
      designation: 'Founder of Dumpy',
    },
    {
      id: 2,
      description:
        'Impressed with master class support of the team and really look forward for the future.',
      name: 'Roman Ul Oman',
      designation: 'Co-founder of QatarDiaries',
    },

    {
      id: 3,
      description:
        'I have bought more than 10 themes on ThemeForest, and this is the first one I review. Wow! Amazing React Theme',
      name: '<PERSON><PERSON>',
      designation: 'Director of Beauty-queen',
    },

    {
      id: 4,
      description:
        'Really, really well made! Love that each component is handmade and customised. Great Work',
      name: '<PERSON><PERSON>',
      designation: 'Co-founder of Softo',
    },

    {
      id: 5,
      description:
        'It written well. The author has a firm understanding of React and other technologies. It been consistently updated. Great product. Thank you.',
      name: '<PERSON>',
      designation: 'Co-founder of Antinio',
    },
  ],
};
export default data;
