import envato from 'common/assets/image/webAppCreative/clients/envato.png';
import evernote from 'common/assets/image/webAppCreative/clients/evernote.png';
import forbes from 'common/assets/image/webAppCreative/clients/forbes.png';
import geekwire from 'common/assets/image/webAppCreative/clients/geekwire.png';
import slack from 'common/assets/image/webAppCreative/clients/slack.png';
import usaToday from 'common/assets/image/webAppCreative/clients/usa-today.png';
import icon1 from 'common/assets/image/webAppCreative/icons/1.png';
import icon2 from 'common/assets/image/webAppCreative/icons/2.png';
import icon3 from 'common/assets/image/webAppCreative/icons/3.png';
import icon4 from 'common/assets/image/webAppCreative/icons/4.png';
import icon5 from 'common/assets/image/webAppCreative/icons/5.png';
import icon6 from 'common/assets/image/webAppCreative/icons/6.png';
import icon7 from 'common/assets/image/webAppCreative/icons/7.png';
import dashboardImg from 'common/assets/image/webAppCreative/dashboard-2.png';
import asana from 'common/assets/image/webAppCreative/icons/asana.png';
import drive from 'common/assets/image/webAppCreative/icons/drive.png';
import dropbox from 'common/assets/image/webAppCreative/icons/dropbox.png';
import fontAwesome from 'common/assets/image/webAppCreative/icons/fontawesome.png';
import github from 'common/assets/image/webAppCreative/icons/github.png';
import googleCloud from 'common/assets/image/webAppCreative/icons/google-cloud.png';
import messenger from 'common/assets/image/webAppCreative/icons/messenger.png';
import nginx from 'common/assets/image/webAppCreative/icons/nginx.png';
import slack2 from 'common/assets/image/webAppCreative/icons/slack.png';
import smashingMag from 'common/assets/image/webAppCreative/icons/smashing-mag.png';
import zeplin from 'common/assets/image/webAppCreative/icons/zeplin.png';
import zoom from 'common/assets/image/webAppCreative/icons/zoom.png';
import icecream from 'common/assets/image/webAppCreative/icons/icecream.png';
import donut from 'common/assets/image/webAppCreative/icons/donut.png';
import pizza from 'common/assets/image/webAppCreative/icons/pizza.png';
import post1 from 'common/assets/image/webAppCreative/post1.png';
import post2 from 'common/assets/image/webAppCreative/post2.png';
import post3 from 'common/assets/image/webAppCreative/post3.png';
import siteLogo from 'common/assets/image/webAppCreative/logo.png';
import facebook from 'common/assets/image/webAppCreative/icons/facebook.png';
import twitter from 'common/assets/image/webAppCreative/icons/twitter.png';
import dribbble from 'common/assets/image/webAppCreative/icons/dribbble.png';

export const menu_items = [
  {
    label: 'Home',
    path: '#home',
    offset: '70',
  },
  {
    label: 'How To',
    path: '#how-to',
    offset: '70',
  },
  {
    label: 'Features',
    path: '#features',
    offset: '70',
  },
  {
    label: 'Testimonial',
    path: '#testimonial',
    offset: '70',
  },
  {
    label: 'Pricing',
    path: '#pricing',
    offset: '70',
  },
  {
    label: 'Faq',
    path: '#faq',
    offset: '70',
  },
];

export const clients = [envato, evernote, forbes, geekwire, slack, usaToday];

export const howTos = [
  {
    id: 1,
    icon: icon1,
    title: 'Manage Smartly',
    text: `Stay on top of your task lists and stay in touch with what's happening`,
    linkLabel: 'Learn More',
    link: '#',
  },
  {
    id: 2,
    icon: icon2,
    title: 'Monitor user Analytics',
    text: `Stay on top of your task lists and stay in touch with what's happening`,
    linkLabel: 'Learn More',
    link: '#',
  },
  {
    id: 3,
    icon: icon3,
    title: 'Safe & Trusted',
    text: `Get the best DoorDash experience with live order tracking.`,
    linkLabel: 'Learn More',
    link: '#',
  },
  {
    id: 4,
    icon: icon4,
    title: 'Fast Customer Support',
    text: `Get the best DoorDash experience with live order tracking.`,
    linkLabel: 'Learn More',
    link: '#',
  },
];

export const analyticsTool = {
  slogan: 'Audience source monitoring',
  title: 'Advanced analytics tools to keep you in control & customizable',
  desc: `Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool.`,
  features: [
    'Chat promt module supported',
    'Enjoy unlimited features by paid plans',
    'Manage ultimate conversation',
  ],
  button: {
    link: '#',
    label: 'Explore More',
  },
};

export const dashboard = {
  sectionTitle: 'Get on the same page, fast.',
  sectionDesc: `Each room is loaded with collaborative surfaces so you can quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with the most advanced theme editor.`,
  tabs: [
    {
      id: 1,
      title: 'Dashboard',
      content: {
        image: dashboardImg,
      },
    },
    {
      id: 2,
      title: 'Whiteboard',
      content: {
        image: dashboardImg,
      },
    },
    {
      id: 3,
      title: 'Transaction',
      content: {
        image: dashboardImg,
      },
    },
    {
      id: 4,
      title: 'Float',
      content: {
        image: dashboardImg,
      },
    },
  ],
};

export const testimonials = [
  {
    id: 1,
    logo: icon5,
    author: 'Johnny Simpson',
    designation: 'Head of Design',
    quote: `Each room is loaded with the most collaborative surfaces so you can quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with the editors.`,
  },
  {
    id: 2,
    logo: icon6,
    author: 'Deanna Hodges',
    designation: 'Business HR Admin',
    quote: `We deliver on such an expansive with innovation agenda with so many theme projects going on at any time, it can be hard to maintain momentum. So We appreciate to work with them.`,
  },
  {
    id: 3,
    logo: icon7,
    author: 'Gracelyn Mason',
    designation: 'Senior Marketer',
    quote: `Flat item is loaded with the most of collaborative surfaces so you can do quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with statics.`,
  },
  {
    id: 4,
    logo: icon5,
    author: 'Gracelyn Mason',
    designation: 'Senior Marketer',
    quote: `Flat item is loaded with the most of collaborative surfaces so you can do quickly take notes, capture to-dos, and share ideas. Pick one of our stock themes, or create your custom theme with statics.`,
  },
];

export const appIntegration = {
  sectionTitle: 'Lets see what we integrate',
  sectionDesc: `We recently had to jump on 10+ different plugin across eight different countries to find the right owner and escalation process.`,
  apps: [
    {
      id: 1,
      icon: nginx,
      name: 'nginx',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 2,
      icon: googleCloud,
      name: 'googleCloud',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 3,
      icon: slack2,
      name: 'slack',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 4,
      icon: dropbox,
      name: 'dropbox',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 5,
      icon: drive,
      name: 'drive',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 6,
      icon: asana,
      name: 'asana',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 7,
      icon: github,
      name: 'github',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 8,
      icon: zeplin,
      name: 'zeplin',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 9,
      icon: nginx,
      name: 'nginx',
      bgColor: null,
      isBlurred: true,
    },
    {
      id: 10,
      icon: messenger,
      name: 'messenger',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 11,
      icon: zoom,
      name: 'zoom',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 12,
      icon: smashingMag,
      name: 'smashingMag',
      bgColor: '#fff',
      isBlurred: false,
    },
    {
      id: 13,
      icon: fontAwesome,
      name: 'fontAwesome',
      bgColor: null,
      isBlurred: false,
    },
    {
      id: 14,
      icon: drive,
      name: 'google drive',
      bgColor: null,
      isBlurred: true,
    },
  ],
};

export const pricing = [
  {
    id: 1,
    price: {
      monthly: 16,
      annual: 16 * 12 - 5,
    },
    currencySymbol: '$',
    isActive: false,
    title: 'Starter Pack',
    desc: 'Complete time tracking & proper reporting',
    icon: icecream,
    button: {
      label: 'Start 6 month trial',
      link: '#',
    },
    details: {
      label: 'What’s include',
      link: '#',
    },
  },
  {
    id: 2,
    price: {
      monthly: 29,
      annual: 29 * 12 - 10,
    },
    currencySymbol: '$',
    isActive: true,
    title: 'Premium Pack',
    desc: 'Effortless team with time management.',
    icon: donut,
    button: {
      label: 'Start 6 month trial',
      link: '#',
    },
    details: {
      label: 'What’s include',
      link: '#',
    },
  },
  {
    id: 3,
    price: {
      monthly: 35,
      annual: 35 * 12 - 15,
    },
    currencySymbol: '$',
    isActive: false,
    title: 'Ultimate Pack',
    desc: 'A custom plan for complex or large organization.',
    icon: pizza,
    button: {
      label: 'Start 6 month trial',
      link: '#',
    },
    details: {
      label: 'What’s include',
      link: '#',
    },
  },
];

export const posts = [
  {
    id: 1,
    date: 'June 3, 2020',
    image: post1,
    title: 'The three Fundamental Rules to Keep Your Website Goal Orientated',
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
  {
    id: 2,
    date: 'Dec 8, 2020',
    image: post2,
    title: 'Five Common Mistakes Teams Make When Tracking Performance',
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
  {
    id: 3,
    date: 'Dec 8, 2020',
    image: post3,
    title: `Why You Might Want to Reconsider with Tracking First Meaningful Paint`,
    excerpt: {
      label: 'Learn More',
      link: '#',
    },
  },
];

export const faqs = [
  {
    id: 1,
    title: '01. What is the process of project final delivery system?',
    description: `Our Customer Experience Team is available 7 days a week and we offer 2 ways to get in contact.Email and Chat. We try to reply quickly, so you need not to wait too long for a response!.`,
  },
  {
    id: 2,
    title: '02. What is payment process, believe in upfront?',
    description: `Please read the documentation carefully. We also have some online  video tutorials regarding this issue. If the problem remains, Please Open a ticket in the support forum.`,
  },
  {
    id: 3,
    title: '03. What is the process of project final delivery system?',
    description: `At first, Please check your internet connection. We also have some online  video tutorials regarding this issue. If the problem remains, Please Open a ticket in the support forum.`,
  },
  {
    id: 4,
    title: '04. Estimate project budget for categories?',
    description: `Our core members created this place for Designers, Developers, Product Owners, Marketing Managers, startup's, Freelancers and really for everyone who appreciates fine designs and well-crafted sites. We want to inspire and support you in the process of creating your own unique website projects.`,
  },
  {
    id: 5,
    title: '05. All about project customization & monetization',
    description: `We are giving the update of this theme continuously. You will receive an email Notification when we push an update. Always try to be updated with us.`,
  },
];

export const footerTop = {
  about: {
    logo: siteLogo,
    text: `We run Advanced Search reports on the criteria you care about to see how work is progressing and where to focus your effort.`,
  },
  widgets: [
    {
      id: 2,
      title: 'About Us',
      list: [
        {
          id: 1,
          title: 'Support Center',
          link: '#',
        },
        {
          id: 2,
          title: 'Customer Support',
          link: '#',
        },
        {
          id: 3,
          title: 'About Us',
          link: '#',
        },
        {
          id: 4,
          title: 'Copyright',
          link: '#',
        },
        {
          id: 5,
          title: 'Popular Campaign',
          link: '#',
        },
      ],
    },
    {
      id: 3,
      title: 'Our Information',
      list: [
        {
          id: 1,
          title: 'Return Policy ',
          link: '#',
        },
        {
          id: 2,
          title: 'Privacy Policy',
          link: '#',
        },
        {
          id: 3,
          title: 'Terms & Conditions',
          link: '#',
        },
        {
          id: 4,
          title: 'Site Map',
          link: '#',
        },
        {
          id: 5,
          title: 'Store Hours',
          link: '#',
        },
      ],
    },
    {
      id: 4,
      title: 'My Account',
      list: [
        {
          id: 1,
          title: 'Press inquiries',
          link: '#',
        },
        {
          id: 2,
          title: 'Social media ',
          link: '#',
        },
        {
          id: 3,
          title: 'directories',
          link: '#',
        },
        {
          id: 4,
          title: 'Images & B-roll',
          link: '#',
        },
        {
          id: 5,
          title: 'Permissions',
          link: '#',
        },
      ],
    },
  ],
  contactInfo: {
    title: 'Contact info',
    address: `Mohakhali DOHS, Amsterdam, Netherlands`,
    phone: `+31 62 19 22 705`,
    openingTime: `7 Days - 8am - 10pm`,
    email: `<EMAIL>`,
  },
};

export const footer = {
  copyright: `Copyright © ${new Date().getFullYear()} Superprops. All rights reserved`,
  nav: [
    {
      id: 1,
      title: 'Support',
      link: '#',
    },
    {
      id: 2,
      title: 'Hiring',
      link: '#',
    },
    {
      id: 3,
      title: 'Privacy',
      link: '#',
    },
    {
      id: 4,
      title: 'Terms',
      link: '#',
    },
  ],
  socialLinks: [
    {
      id: 1,
      link: 'http://facebook.com',
      icon: facebook,
      label: 'Facebook',
    },
    {
      id: 2,
      link: 'http://twitter.com',
      icon: twitter,
      label: 'Twitter',
    },
    {
      id: 3,
      link: 'http://dribbble.com',
      icon: dribbble,
      label: 'Dribbble',
    },
  ],
};
