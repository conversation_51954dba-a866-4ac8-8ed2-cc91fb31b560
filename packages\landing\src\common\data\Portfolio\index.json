{"SOCIAL_PROFILES": [{"icon": "flaticon-facebook-logo", "url": "#"}, {"icon": "flaticon-twitter-logo-silhouette", "url": "#"}, {"icon": "flaticon-instagram", "url": "#"}, {"icon": "flaticon-dribble-logo", "url": "#"}], "MENU_ITEMS": [{"label": "ME", "path": "#banner_section", "offset": "0"}, {"label": "PROJECT", "path": "#portfolio_section", "offset": "0"}, {"label": "AWARDS", "path": "#awards_section", "offset": "0"}, {"label": "WHY ME?", "path": "#process_section", "offset": "0"}], "AWARDS": [{"awardLogo": "../../assets/image/portfolio/award-1.png", "awardName": "Free Software Advice", "awardDetails": "Top Rated App Development Companies USA", "awardeeLogo": "../../assets/image/portfolio/awardee-1.png", "awardeeName": "Awardee", "date": "The Jury 2018"}, {"awardLogo": "../../assets/image/portfolio/award-2.png", "awardName": "Free Software Advice", "awardDetails": "Top Rated App Development Companies USA", "awardeeLogo": "../../assets/image/portfolio/awardee-2.png", "awardeeName": "Awardee", "date": "The Jury 2018"}, {"awardLogo": "../../assets/image/portfolio/award-3.png", "awardName": "Free Software Advice", "awardDetails": "Top Rated App Development Companies USA", "awardeeLogo": "../../assets/image/portfolio/awardee-3.png", "awardeeName": "Awardee", "date": "The Jury 2018"}, {"awardLogo": "../../assets/image/portfolio/award-4.png", "awardName": "Free Software Advice", "awardDetails": "Top Rated App Development Companies USA", "awardeeLogo": "../../assets/image/portfolio/awardee-4.png", "awardeeName": "Awardee", "date": "The Jury 2018"}], "PORTFOLIO_SHOWCASE": [{"title": "DESIGN", "portfolioItem": [{"title": "Canada Media Site", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-1.jpg", "link": "#", "featuredIn": "AWWWARDS", "featuredLink": "#", "view": "4.5K", "love": "1.5K", "feedback": "1.2K", "buildWith": [{"content": "React JS"}, {"content": "Next JS"}, {"content": "Styled Component"}]}, {"title": "RedQ, Inc. mobile app", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-2.jpg", "link": "#", "featuredIn": "AppStore", "featuredLink": "#", "view": "8.5K", "love": "5.5K", "feedback": "3.2K", "buildWith": [{"content": "React Native"}, {"content": "Firebase"}, {"content": "Styled Component"}]}]}, {"title": "DEVELOPMENT", "portfolioItem": [{"title": "Canada Media Site", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-1.jpg", "link": "#", "featuredIn": "AWWWARDS", "featuredLink": "#", "view": "4.5K", "love": "1.5K", "feedback": "1.2K", "buildWith": [{"content": "React JS"}, {"content": "Next JS"}, {"content": "Styled Component"}]}, {"title": "RedQ, Inc. mobile app", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-2.jpg", "link": "#", "featuredIn": "AppStore", "featuredLink": "#", "view": "8.5K", "love": "5.5K", "feedback": "3.2K", "buildWith": [{"content": "React Native"}, {"content": "Firebase"}, {"content": "Styled Component"}]}]}, {"title": "ANIMATION", "portfolioItem": [{"title": "Canada Media Site", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-1.jpg", "link": "#", "featuredIn": "AWWWARDS", "featuredLink": "#", "view": "4.5K", "love": "1.5K", "feedback": "1.2K", "buildWith": [{"content": "React JS"}, {"content": "Next JS"}, {"content": "Styled Component"}]}, {"title": "RedQ, Inc. mobile app", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-2.jpg", "link": "#", "featuredIn": "AppStore", "featuredLink": "#", "view": "8.5K", "love": "5.5K", "feedback": "3.2K", "buildWith": [{"content": "React Native"}, {"content": "Firebase"}, {"content": "Styled Component"}]}]}, {"title": "TV ADVERTISEMENT", "portfolioItem": [{"title": "Canada Media Site", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-1.jpg", "link": "#", "featuredIn": "AWWWARDS", "featuredLink": "#", "view": "4.5K", "love": "1.5K", "feedback": "1.2K", "buildWith": [{"content": "React JS"}, {"content": "Next JS"}, {"content": "Styled Component"}]}, {"title": "RedQ, Inc. mobile app", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-2.jpg", "link": "#", "featuredIn": "AppStore", "featuredLink": "#", "view": "8.5K", "love": "5.5K", "feedback": "3.2K", "buildWith": [{"content": "React Native"}, {"content": "Firebase"}, {"content": "Styled Component"}]}]}, {"title": "MARKETING", "portfolioItem": [{"title": "Canada Media Site", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-1.jpg", "link": "#", "featuredIn": "AWWWARDS", "featuredLink": "#", "view": "4.5K", "love": "1.5K", "feedback": "1.2K", "buildWith": [{"content": "React JS"}, {"content": "Next JS"}, {"content": "Styled Component"}]}, {"title": "RedQ, Inc. mobile app", "description": "An effective and immersive user experience is what catches the attention and spreads a clear message. That's why we attach great importance to the fact that ergonomics serves the design, and that this design is innovative and neat.", "image": "../../assets/image/portfolio/portfolio-2.jpg", "link": "#", "featuredIn": "AppStore", "featuredLink": "#", "view": "8.5K", "love": "5.5K", "feedback": "3.2K", "buildWith": [{"content": "React Native"}, {"content": "Firebase"}, {"content": "Styled Component"}]}]}], "PROCESS_STEPS": [{"image": "../../assets/image/portfolio/step-1.png", "title": "1. Research", "description": "We work with you to understand user’s stories and validate your idea with real users using lean design sprints."}, {"image": "../../assets/image/portfolio/step-2.png", "title": "2. Design", "description": "Expanding on the insights gained, you’ll work closely with our design team to create an elegant design"}, {"image": "../../assets/image/portfolio/step-3.png", "title": "3. Build", "description": "With our scrum-based agile methodology, you’ll receive iterative builds every two weeks, which gives you "}], "SERVICE_LIST": [{"title": "UI/UX Design", "listItems": [{"content": "Design Sprints"}, {"content": "User Research"}, {"content": "Visual Design"}, {"content": "Mobile App Design"}, {"content": "Tracking & Learning"}, {"content": "Building Traction"}]}, {"title": "Web Development", "listItems": [{"content": "ReactJS"}, {"content": "AngularJS"}, {"content": "ASP.NET MVC"}, {"content": "WordPress"}, {"content": "NodeJS"}, {"content": "GO"}]}, {"title": "Mobile App Development", "listItems": [{"content": "iOS"}, {"content": "Android"}, {"content": "React Native"}, {"content": "Ionic & Apache Cordova"}, {"content": "NodeJS"}, {"content": "3D & VR"}]}], "SKILLS": [{"title": "Graphic Design", "description": "<PERSON> maintained the sharp distinction between science and the practical", "icon": "../../assets/image/portfolio/skill-1.svg", "successRate": "90"}, {"title": "UI/UX Design", "description": "<PERSON> maintained the sharp distinction between science and the practical", "icon": "../../assets/image/portfolio/skill-2.svg", "successRate": "85"}, {"title": "Web Application", "description": "<PERSON> maintained the sharp distinction between science and the practical", "icon": "../../assets/image/portfolio/skill-3.svg", "successRate": "80"}, {"title": "Mobile Application", "description": "<PERSON> maintained the sharp distinction between science and the practical", "icon": "../../assets/image/portfolio/skill-4.svg", "successRate": "70"}], "CLIENTS": [{"image": "../../assets/image/portfolio/client-1.png", "title": "Microsoft"}, {"image": "../../assets/image/portfolio/client-2.png", "title": "Airbnb"}, {"image": "../../assets/image/portfolio/client-3.png", "title": "Adidas"}, {"image": "../../assets/image/portfolio/client-4.png", "title": "IBM"}, {"image": "../../assets/image/portfolio/client-5.png", "title": "Amazon"}, {"image": "../../assets/image/portfolio/client-6.png", "title": "Google"}], "TESTIMONIAL": [{"image": "../../assets/image/portfolio/client-avatar-1.jpg", "review": "Another quality React-based product from RedQ Team. Manage to turn highly complex tech into simple components.", "name": "<PERSON>", "designation": "Founder & CEO", "twitterProfile": "https://twitter.com/redqinc", "organization": "@Tonquin", "organizationURL": "https://redq.io/"}, {"image": "../../assets/image/portfolio/client-avatar-2.jpg", "review": "Another quality React-based product from RedQ Team. Manage to turn highly complex tech into simple components.", "name": "<PERSON><PERSON>", "designation": "Co-Founder & CTO", "twitterProfile": "https://twitter.com/redqinc", "organization": "@Tonquin", "organizationURL": "https://redq.io/"}, {"image": "../../assets/image/portfolio/client-avatar-3.jpg", "review": "Another quality React-based product from RedQ Team. Manage to turn highly complex tech into simple components.", "name": "<PERSON>", "designation": "Co-Founder & COO", "twitterProfile": "https://twitter.com/redqinc", "organization": "@Tonquin", "organizationURL": "https://redq.io/"}], "FOOTER_MENU": [{"label": "Contact", "path": "#"}, {"label": "Privacy", "path": "#"}, {"label": "<PERSON><PERSON>", "path": "#"}]}