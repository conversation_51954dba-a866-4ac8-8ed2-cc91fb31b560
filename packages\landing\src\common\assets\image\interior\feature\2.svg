<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="98" height="105" viewBox="0 0 98 105">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_2836" data-name="Path 2836" d="M64.353-43.143a3.642,3.642,0,0,1-3.642-3.642A1.214,1.214,0,0,0,59.5-48a1.214,1.214,0,0,0-1.214,1.214,3.643,3.643,0,0,1-3.642,3.642A3.642,3.642,0,0,1,51-46.786,1.214,1.214,0,0,0,49.784-48a1.214,1.214,0,0,0-1.214,1.214,3.643,3.643,0,0,1-3.642,3.642,3.642,3.642,0,0,1-3.642-3.642A1.214,1.214,0,0,0,40.07-48a1.214,1.214,0,0,0-1.214,1.214,3.643,3.643,0,0,1-3.642,3.642,3.642,3.642,0,0,1-3.642-3.642A1.214,1.214,0,0,0,30.357-48a1.214,1.214,0,0,0-1.214,1.214A3.643,3.643,0,0,1,25.5-43.143a3.642,3.642,0,0,1-3.642-3.642A1.214,1.214,0,0,0,20.644-48a1.214,1.214,0,0,0-1.214,1.214,3.643,3.643,0,0,1-3.642,3.642,3.642,3.642,0,0,1-3.642-3.642A1.214,1.214,0,0,0,10.931-48a1.214,1.214,0,0,0-1.214,1.214,3.643,3.643,0,0,1-3.642,3.642,3.642,3.642,0,0,1-3.642-3.642A1.214,1.214,0,0,0,1.218-48,1.214,1.214,0,0,0,0-46.786,6.052,6.052,0,0,0,4.16-41.02a6.053,6.053,0,0,0,6.771-2.16,6.015,6.015,0,0,0,4.857,2.466,6.016,6.016,0,0,0,4.857-2.466A6.015,6.015,0,0,0,25.5-40.715a6.016,6.016,0,0,0,4.857-2.466,6.015,6.015,0,0,0,4.857,2.466,6.016,6.016,0,0,0,4.857-2.466,6.015,6.015,0,0,0,4.857,2.466,6.016,6.016,0,0,0,4.857-2.466,6.015,6.015,0,0,0,4.857,2.466A6.016,6.016,0,0,0,59.5-43.181a6.048,6.048,0,0,0,4.857,2.466,1.214,1.214,0,0,0,1.214-1.214A1.214,1.214,0,0,0,64.353-43.143Z" transform="translate(-0.004 48)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_2838" data-name="Path 2838" d="M296-99.143a1.214,1.214,0,0,0,1.214,1.214,4.826,4.826,0,0,0,3.642-1.678,4.791,4.791,0,0,0,3.642,1.678,4.793,4.793,0,0,0,3.642-1.678,4.824,4.824,0,0,0,3.642,1.678A1.214,1.214,0,0,0,313-99.143a1.214,1.214,0,0,0-1.214-1.214,2.428,2.428,0,0,1-2.428-2.428A1.214,1.214,0,0,0,308.145-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428,2.428,2.428,0,0,1-2.428-2.428A1.214,1.214,0,0,0,300.86-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428A1.214,1.214,0,0,0,296-99.143Z" transform="translate(-296.004 104)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <path id="Path_2840" data-name="Path 2840" d="M444.86-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428A1.214,1.214,0,0,0,440-99.143a1.214,1.214,0,0,0,1.214,1.214,4.857,4.857,0,0,0,4.857-4.857A1.214,1.214,0,0,0,444.86-104Z" transform="translate(-440.004 104)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <path id="Path_2842" data-name="Path 2842" d="M203.646-102.786a2.428,2.428,0,0,1-2.428,2.428A1.214,1.214,0,0,0,200-99.143a1.214,1.214,0,0,0,1.214,1.214,4.826,4.826,0,0,0,3.642-1.678,4.836,4.836,0,0,0,5.357,1.372,4.835,4.835,0,0,0,3.142-4.551A1.214,1.214,0,0,0,212.145-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428,2.428,2.428,0,0,1-2.428-2.428A1.214,1.214,0,0,0,204.86-104a1.214,1.214,0,0,0-1.214,1.214Z" transform="translate(-200.004 104)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <path id="Path_2844" data-name="Path 2844" d="M57.218-97.929a4.826,4.826,0,0,0,3.642-1.678A4.791,4.791,0,0,0,64.5-97.929a4.793,4.793,0,0,0,3.642-1.678A4.836,4.836,0,0,0,73.5-98.235a4.835,4.835,0,0,0,3.142-4.551A1.214,1.214,0,0,0,75.43-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428,2.428,2.428,0,0,1-2.428-2.428A1.214,1.214,0,0,0,68.145-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428,2.428,2.428,0,0,1-2.428-2.428A1.214,1.214,0,0,0,60.86-104a1.214,1.214,0,0,0-1.214,1.214,2.428,2.428,0,0,1-2.428,2.428A1.214,1.214,0,0,0,56-99.143,1.214,1.214,0,0,0,57.218-97.929Z" transform="translate(-56.004 104)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <path id="Path_2846" data-name="Path 2846" d="M53.917-435.108a13.4,13.4,0,0,0-12.635-9.059,13.261,13.261,0,0,0-9.973,4.493c-.533-.153-1.1-.292-1.725-.419a20.209,20.209,0,0,0-4.085-.431c-.486,0-1.007.029-1.542.074a24.416,24.416,0,0,1-.886-6.466,24.132,24.132,0,0,1,4.622-14.249l23.9,13.8a1.215,1.215,0,0,0,1.2.011,1.213,1.213,0,0,0,.626-1.018,19.4,19.4,0,0,0-5.277-14.075,19.4,19.4,0,0,0-13.747-6.08l1.336-.772,10.821-6.246a1.215,1.215,0,0,0,.607-1.029,1.216,1.216,0,0,0-.569-1.051,15.339,15.339,0,0,0-12.56-1.716,15.338,15.338,0,0,0-9.561,8.324,13.218,13.218,0,0,0-8.262-7.073,13.217,13.217,0,0,0-10.77,1.519,1.215,1.215,0,0,0-.569,1.051,1.214,1.214,0,0,0,.606,1.029l10.342,5.971a16.264,16.264,0,0,0-11.422,5.149A16.264,16.264,0,0,0,.013-451.627a1.214,1.214,0,0,0,.626,1.018,1.213,1.213,0,0,0,1.2-.011l14.775-8.53a26.785,26.785,0,0,0-4.675,23.762,20.528,20.528,0,0,0-7.077,15.506c0,.146.009.288.019.43l.011.212A1.215,1.215,0,0,0,6.1-418.062h.037a1.214,1.214,0,0,0,1.178-1.248c0-.1-.009-.209-.017-.312s-.014-.172-.014-.261a18.107,18.107,0,0,1,5.5-13,26.789,26.789,0,0,0,2.285,4.547,1.215,1.215,0,0,0,1.676.379,1.216,1.216,0,0,0,.379-1.676,24.29,24.29,0,0,1-2.109-4.209H17a1.214,1.214,0,0,0,1.214-1.214A1.214,1.214,0,0,0,17-436.275H14.214a24.43,24.43,0,0,1-.688-3.617h3.22a1.214,1.214,0,0,0,1.214-1.214,1.214,1.214,0,0,0-1.214-1.214H13.368c0-.11-.012-.219-.012-.329a24.663,24.663,0,0,1,.228-3.339h2.2A1.214,1.214,0,0,0,17-447.2a1.214,1.214,0,0,0-1.214-1.214H14.055A24.27,24.27,0,0,1,24.173-462.9c.04,0,.081.015.122.015a1.232,1.232,0,0,0,.331-.049l.958.554a26.745,26.745,0,0,0,2.733,34.239,1.215,1.215,0,0,0,1.705,0,1.215,1.215,0,0,0,.029-1.7,24.332,24.332,0,0,1-5.311-8.229c.256-.012.516-.027.759-.027a17.871,17.871,0,0,1,3.6.378,18.6,18.6,0,0,1,2.234.59,1.214,1.214,0,0,0,1.347-.4,10.908,10.908,0,0,1,10.715-4,10.907,10.907,0,0,1,8.4,7.766,1.215,1.215,0,0,0,.9.857,13.435,13.435,0,0,1,10.439,13.025c0,.1-.008.194-.015.29l-.014.267a1.214,1.214,0,0,0,1.163,1.264h.051a1.215,1.215,0,0,0,1.214-1.164l.011-.2c.011-.151.021-.3.021-.455A15.889,15.889,0,0,0,53.917-435.108ZM42.5-463.826a16.954,16.954,0,0,1,8.43,13.28l-5.767-3.334,1.214-2.1a1.214,1.214,0,0,0,.005-1.219,1.214,1.214,0,0,0-1.055-.609,1.214,1.214,0,0,0-1.053.614l-1.214,2.1-3.157-1.821,1.214-2.1a1.213,1.213,0,0,0,.005-1.219,1.214,1.214,0,0,0-1.055-.609,1.215,1.215,0,0,0-1.053.614l-1.214,2.1-3.157-1.821,1.214-2.1a1.214,1.214,0,0,0,.006-1.219,1.214,1.214,0,0,0-1.056-.609,1.215,1.215,0,0,0-1.053.614l-1.214,2.1-5.464-3.157,1.646-.95A16.958,16.958,0,0,1,42.5-463.826Zm-10.55-12a12.892,12.892,0,0,1,11.466-.719l-3.778,2.185-.807-1.4a1.215,1.215,0,0,0-1.655-.438,1.214,1.214,0,0,0-.448,1.652l.807,1.4L35.562-472l-.806-1.4a1.215,1.215,0,0,0-1.053-.614,1.213,1.213,0,0,0-1.056.609,1.213,1.213,0,0,0,.006,1.218l.806,1.4-1.978,1.142-.806-1.4a1.214,1.214,0,0,0-1.659-.445,1.215,1.215,0,0,0-.445,1.659l.806,1.4-1.671.964a19.555,19.555,0,0,0-2.009.818c0-.026-.009-.05-.011-.075a12.872,12.872,0,0,1,6.269-9.1ZM17.915-474.73a10.814,10.814,0,0,1,5.283,7.673l-4.857-2.8.925-1.6a1.212,1.212,0,0,0,.005-1.219,1.214,1.214,0,0,0-1.055-.609,1.215,1.215,0,0,0-1.053.614l-.926,1.6-1.378-.8.925-1.6a1.214,1.214,0,0,0-.449-1.651,1.213,1.213,0,0,0-1.654.437l-.933,1.6-4.122-2.382a10.817,10.817,0,0,1,9.288.738ZM9.349-464.246a13.824,13.824,0,0,1,12.446-.708L17.562-462.5l-.9-1.56A1.213,1.213,0,0,0,15-464.505a1.215,1.215,0,0,0-.445,1.659l.9,1.56-2.249,1.292-.9-1.56A1.214,1.214,0,0,0,10.652-462a1.215,1.215,0,0,0-.445,1.659l.9,1.56-2.244,1.3-.9-1.559a1.215,1.215,0,0,0-1.659-.445,1.215,1.215,0,0,0-.445,1.659l.9,1.559-4.238,2.446A13.822,13.822,0,0,1,9.349-464.246Z" transform="translate(0 479.981)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <path id="Path_2848" data-name="Path 2848" d="M361.218-421.572a2.428,2.428,0,0,1,2.428,2.428,1.214,1.214,0,0,0,1.214,1.214,1.214,1.214,0,0,0,1.214-1.214,2.428,2.428,0,0,1,2.428-2.428,1.214,1.214,0,0,0,1.214-1.214A1.214,1.214,0,0,0,368.5-424a4.845,4.845,0,0,0-3.642,1.647A4.847,4.847,0,0,0,361.218-424,1.214,1.214,0,0,0,360-422.786,1.214,1.214,0,0,0,361.218-421.572Z" transform="translate(-360.004 424)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <path id="Path_2850" data-name="Path 2850" d="M420.86-361.929a1.214,1.214,0,0,0,1.214-1.214,2.428,2.428,0,0,1,2.428-2.428,1.214,1.214,0,0,0,1.214-1.214A1.214,1.214,0,0,0,424.5-368a4.845,4.845,0,0,0-3.642,1.647A4.847,4.847,0,0,0,417.218-368,1.214,1.214,0,0,0,416-366.786a1.214,1.214,0,0,0,1.214,1.214,2.428,2.428,0,0,1,2.428,2.428A1.214,1.214,0,0,0,420.86-361.929Z" transform="translate(-416.004 368)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <path id="Path_2852" data-name="Path 2852" d="M409.218-477.572a2.428,2.428,0,0,1,2.428,2.428,1.214,1.214,0,0,0,1.214,1.214,1.214,1.214,0,0,0,1.214-1.214,2.428,2.428,0,0,1,2.428-2.428,1.214,1.214,0,0,0,1.214-1.214A1.214,1.214,0,0,0,416.5-480a4.845,4.845,0,0,0-3.642,1.647A4.847,4.847,0,0,0,409.218-480,1.214,1.214,0,0,0,408-478.786,1.214,1.214,0,0,0,409.218-477.572Z" transform="translate(-408.004 480)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <path id="Path_2854" data-name="Path 2854" d="M226.432-182.778a1.214,1.214,0,0,1-1.214,1.214A1.214,1.214,0,0,1,224-182.778a1.214,1.214,0,0,1,1.214-1.214A1.214,1.214,0,0,1,226.432-182.778Z" transform="translate(-224.004 183.992)" fill="#352fd9"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <path id="Path_2856" data-name="Path 2856" d="M298.432-166.778a1.214,1.214,0,0,1-1.214,1.214A1.214,1.214,0,0,1,296-166.778a1.214,1.214,0,0,1,1.214-1.214A1.214,1.214,0,0,1,298.432-166.778Z" transform="translate(-296.004 167.992)" fill="#352fd9"/>
    </clipPath>
  </defs>
  <g id="Group_3645" data-name="Group 3645" transform="translate(-907 -1232)">
    <circle id="Ellipse_66" data-name="Ellipse 66" cx="45" cy="45" r="45" transform="translate(915 1247)" fill="#e1e0f9"/>
    <circle id="Ellipse_68" data-name="Ellipse 68" cx="24.5" cy="24.5" r="24.5" transform="translate(907 1232)" fill="#fdef00" opacity="0.299"/>
    <g id="Group_3643" data-name="Group 3643" transform="translate(923.694 1735.461)" opacity="0.698">
      <g id="Group_3632" data-name="Group 3632" transform="translate(0.001 -414.436)" clip-path="url(#clip-path)">
        <path id="Path_2835" data-name="Path 2835" d="M-5-44.2H62.085V-53H-5Z" transform="translate(4.237 52.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3633" data-name="Group 3633" transform="translate(44.924 -422.935)" clip-path="url(#clip-path-2)">
        <path id="Path_2837" data-name="Path 2837" d="M291-101.412H309.52V-109H291Z" transform="translate(-291.763 108.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3634" data-name="Group 3634" transform="translate(66.779 -422.935)" clip-path="url(#clip-path-3)">
        <path id="Path_2839" data-name="Path 2839" d="M435-101.412h7.588V-109H435Z" transform="translate(-435.763 108.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3635" data-name="Group 3635" transform="translate(30.354 -422.935)" clip-path="url(#clip-path-4)">
        <path id="Path_2841" data-name="Path 2841" d="M195-101.411h14.873V-109H195Z" transform="translate(-195.763 108.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3636" data-name="Group 3636" transform="translate(8.5 -422.935)" clip-path="url(#clip-path-5)">
        <path id="Path_2843" data-name="Path 2843" d="M51-101.411H73.162V-109H51Z" transform="translate(-51.763 108.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3637" data-name="Group 3637" transform="translate(0 -479.997)" clip-path="url(#clip-path-6)">
        <path id="Path_2845" data-name="Path 2845" d="M-5-421.545H62.085v-63.436H-5Z" transform="translate(4.241 484.222)" fill="#352fd9"/>
      </g>
      <g id="Group_3638" data-name="Group 3638" transform="translate(54.637 -471.501)" clip-path="url(#clip-path-7)">
        <path id="Path_2847" data-name="Path 2847" d="M355-421.412h11.231V-429H355Z" transform="translate(-355.763 428.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3639" data-name="Group 3639" transform="translate(63.136 -463.002)" clip-path="url(#clip-path-8)">
        <path id="Path_2849" data-name="Path 2849" d="M411-365.412h11.231V-373H411Z" transform="translate(-411.763 372.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3640" data-name="Group 3640" transform="translate(61.922 -480)" clip-path="url(#clip-path-9)">
        <path id="Path_2851" data-name="Path 2851" d="M403-477.412h11.231V-485H403Z" transform="translate(-403.763 484.241)" fill="#352fd9"/>
      </g>
      <g id="Group_3641" data-name="Group 3641" transform="translate(33.997 -435.076)" clip-path="url(#clip-path-10)">
        <path id="Path_2853" data-name="Path 2853" d="M219-185.046h3.946v-3.946H219Z" transform="translate(-219.763 188.233)" fill="#352fd9"/>
      </g>
      <g id="Group_3642" data-name="Group 3642" transform="translate(44.924 -432.647)" clip-path="url(#clip-path-11)">
        <path id="Path_2855" data-name="Path 2855" d="M291-169.046h3.946v-3.946H291Z" transform="translate(-291.763 172.233)" fill="#352fd9"/>
      </g>
    </g>
  </g>
</svg>
