<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="151" height="46" viewBox="0 0 151 46">
  <defs>
    <linearGradient id="linear-gradient" x1="0.807" y1="0.5" x2="-1.573" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ec942d"/>
      <stop offset="1" stop-color="#fbde1e"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.862" y1="0.178" x2="-0.501" y2="1.948" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#dd4245"/>
      <stop offset="1" stop-color="#b72162"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-0.507" y1="-0.956" x2="0.606" y2="0.489" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6cb966"/>
      <stop offset="1" stop-color="#299567"/>
    </linearGradient>
  </defs>
  <g id="play-store-download" transform="translate(0.5 0.5)">
    <g id="Medium-Product-Hunt">
      <g id="Group">
        <path id="Fill-3" d="M144.444,45H5.556A5.607,5.607,0,0,1,0,39.375V5.625A5.607,5.607,0,0,1,5.556,0H144.444A5.607,5.607,0,0,1,150,5.625v33.75A5.607,5.607,0,0,1,144.444,45" fill="none" stroke="#fff" stroke-width="1" fill-rule="evenodd"/>
        <path id="Fill-5" d="M75.967,15.3a3.067,3.067,0,0,1-.829,2.253,3.209,3.209,0,0,1-2.449,1,3.333,3.333,0,0,1-2.453-1.014,3.417,3.417,0,0,1-1.01-2.512,3.415,3.415,0,0,1,1.01-2.512A3.333,3.333,0,0,1,72.69,11.5a3.415,3.415,0,0,1,1.369.282,2.744,2.744,0,0,1,1.042.755l-.586.593a2.241,2.241,0,0,0-1.826-.8,2.513,2.513,0,0,0-1.82.749,2.6,2.6,0,0,0-.769,1.946,2.6,2.6,0,0,0,.769,1.946,2.632,2.632,0,0,0,3.682,0,2.139,2.139,0,0,0,.559-1.368H72.69v-.81h3.231a2.917,2.917,0,0,1,.047.508" transform="translate(-23.28 -3.776)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-6" d="M75.911,15.245H75.8a2.953,2.953,0,0,1-.8,2.174h0a3.1,3.1,0,0,1-2.369.964,3.22,3.22,0,0,1-2.374-.981,3.3,3.3,0,0,1-.978-2.432,3.3,3.3,0,0,1,.978-2.432,3.22,3.22,0,0,1,2.374-.981,3.291,3.291,0,0,1,1.324.273,2.638,2.638,0,0,1,1,.723l.084-.072-.079-.08L74.38,13l.079.079L74.543,13a2.349,2.349,0,0,0-1.91-.84,2.629,2.629,0,0,0-1.9.781,2.713,2.713,0,0,0-.8,2.027,2.713,2.713,0,0,0,.8,2.027,2.74,2.74,0,0,0,3.839-.006,2.25,2.25,0,0,0,.591-1.437l.01-.123H72.744v-.586h3.12v-.111l-.11.019a2.827,2.827,0,0,1,.046.489h.222a3.031,3.031,0,0,0-.049-.529l-.017-.092H72.522v1.035h2.532v-.113l-.111-.009a2.025,2.025,0,0,1-.527,1.3,2.521,2.521,0,0,1-3.527,0,2.487,2.487,0,0,1-.734-1.866A2.487,2.487,0,0,1,70.89,13.1a2.4,2.4,0,0,1,1.743-.717,2.124,2.124,0,0,1,1.74.761l.078.093.672-.681.072-.072-.066-.08a2.868,2.868,0,0,0-1.084-.785,3.529,3.529,0,0,0-1.412-.291A3.441,3.441,0,0,0,70.1,12.379a3.529,3.529,0,0,0-1.043,2.592A3.533,3.533,0,0,0,70.1,17.564a3.444,3.444,0,0,0,2.531,1.045,3.327,3.327,0,0,0,2.53-1.034l-.081-.078.079.08a3.188,3.188,0,0,0,.861-2.333h-.111" transform="translate(-23.223 -3.721)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-7" d="M85.127,12.554H82.09v2.141h2.738v.81H82.09v2.141h3.037v.829H81.234v-6.75h3.892v.829" transform="translate(-27.318 -3.85)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-8" d="M85.07,12.5v-.113H81.923v2.366H84.66v.585H81.923V17.7h3.037v.6h-3.67V11.783h3.67V12.5h.111v0h.111v-.942H81.067v6.975h4.114V17.478H82.145V15.563h2.738V14.528H82.145V12.612h3.037V12.5H85.07" transform="translate(-27.261 -3.795)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-9" d="M91.164,18.475h-.858V12.554H88.445v-.829h4.581v.829H91.164v5.921" transform="translate(-29.742 -3.85)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-10" d="M91.107,18.42v-.112H90.36V12.387H88.5v-.6h4.36v.6H91V18.42h.111v0h.111V12.612H93.08V11.557h-4.8v1.054h1.862v5.921h1.08V18.42h-.111" transform="translate(-29.686 -3.795)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-11" d="M100.341,18.475h.857v-6.75h-.857Z" transform="translate(-33.743 -3.85)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-12" d="M100.284,18.42h.111V11.783h.633v6.525h-.744v.112h0v.113h.968V11.557h-1.079v6.975h.111V18.42" transform="translate(-33.687 -3.795)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-13" d="M105.977,18.475h-.857V12.554h-1.862v-.829h4.581v.829h-1.862v5.921" transform="translate(-34.724 -3.85)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-14" d="M105.921,18.42v-.112h-.746V12.387h-1.862v-.6h4.359v.6H105.81V18.42h.111v0h.111V12.612h1.862V11.557h-4.8v1.054h1.862v5.921h1.079V18.42h-.111" transform="translate(-34.668 -3.795)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-15" d="M116,16.964a2.542,2.542,0,0,0,3.622,0,2.662,2.662,0,0,0,.741-1.937,2.662,2.662,0,0,0-.741-1.937,2.542,2.542,0,0,0-3.622,0,2.665,2.665,0,0,0-.74,1.937A2.665,2.665,0,0,0,116,16.964Zm4.256.566a3.432,3.432,0,0,1-4.888,0,3.475,3.475,0,0,1-.982-2.5,3.475,3.475,0,0,1,.982-2.5,3.421,3.421,0,0,1,4.883,0,3.474,3.474,0,0,1,.987,2.5A3.479,3.479,0,0,1,120.253,17.529Z" transform="translate(-38.465 -3.776)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-16" d="M115.941,16.909l-.079.079a2.648,2.648,0,0,0,3.78,0,2.769,2.769,0,0,0,.773-2.016,2.771,2.771,0,0,0-.773-2.016,2.651,2.651,0,0,0-3.78,0,2.779,2.779,0,0,0-.772,2.016,2.777,2.777,0,0,0,.772,2.016l.159-.158a2.546,2.546,0,0,1-.709-1.859,2.546,2.546,0,0,1,.709-1.858h0a2.433,2.433,0,0,1,3.464,0,2.548,2.548,0,0,1,.709,1.858,2.548,2.548,0,0,1-.709,1.859,2.43,2.43,0,0,1-3.464,0Zm4.256.566-.079-.079a3.321,3.321,0,0,1-4.729,0l0,0,0,0a3.361,3.361,0,0,1-.951-2.424,3.361,3.361,0,0,1,.951-2.424,3.311,3.311,0,0,1,4.724,0,3.361,3.361,0,0,1,.954,2.42,3.364,3.364,0,0,1-.95,2.424l.159.156a3.589,3.589,0,0,0,1.013-2.581,3.588,3.588,0,0,0-1.018-2.576,3.53,3.53,0,0,0-5.043,0,3.591,3.591,0,0,0-1.013,2.581,3.589,3.589,0,0,0,1.013,2.581l0,0,0,0a3.541,3.541,0,0,0,5.048,0Z" transform="translate(-38.409 -3.721)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-17" d="M126.518,18.475v-6.75h1.043l3.24,5.25h.037l-.037-1.3v-3.95h.857v6.75h-.894l-3.389-5.506h-.038l.038,1.3v4.2h-.857" transform="translate(-42.546 -3.85)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-18" d="M126.462,18.42h.111V11.783h.87l3.24,5.252h.213l-.04-1.415V11.783h.634v6.525h-.721L127.38,12.8h-.212l.04,1.415v4.09h-.746v.112h0v.113h.968V14.214l-.038-1.3-.111,0v.112h.038v-.112l-.094.06,3.422,5.559h1.067V11.557h-1.079v4.065l.037,1.3.111,0v-.111h-.037v.111l.094-.058-3.273-5.3h-1.216v6.975h.111V18.42" transform="translate(-42.49 -3.795)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-19" d="M141.923,43.126H144V29.062h-2.073Zm18.674-9-2.377,6.1h-.071l-2.467-6.1H153.45l3.7,8.522-2.109,4.741H157.2l5.7-13.263Zm-11.758,7.4c-.68,0-1.627-.343-1.627-1.194,0-1.086,1.18-1.5,2.2-1.5a3.66,3.66,0,0,1,1.894.47A2.524,2.524,0,0,1,148.84,41.528Zm.25-7.707a3.884,3.884,0,0,0-3.7,2.153l1.84.777a1.96,1.96,0,0,1,1.894-1.032,2,2,0,0,1,2.18,1.81v.144a4.542,4.542,0,0,0-2.162-.542c-1.983,0-4,1.1-4,3.166a3.225,3.225,0,0,0,3.449,3.095,2.917,2.917,0,0,0,2.646-1.376h.071V43.1h2V37.711C153.306,35.214,151.466,33.821,149.09,33.821ZM136.276,35.84h-2.949V31.02h2.949a2.41,2.41,0,1,1,0,4.821Zm-.053-6.778h-4.968V43.126h2.072V37.8h2.9a4.372,4.372,0,1,0,0-8.736Zm-27.09,12.468a2.913,2.913,0,0,1,0-5.8,2.684,2.684,0,0,1,2.523,2.918A2.66,2.66,0,0,1,109.133,41.531Zm2.38-6.615h-.071a3.257,3.257,0,0,0-2.488-1.069,4.792,4.792,0,0,0,0,9.569,3.2,3.2,0,0,0,2.488-1.088h.071v.688c0,1.832-.966,2.81-2.523,2.81a2.619,2.619,0,0,1-2.381-1.7l-1.808.762a4.506,4.506,0,0,0,4.189,2.827c2.434,0,4.493-1.45,4.493-4.984V34.136h-1.97Zm3.4,8.21h2.077V29.061h-2.077Zm5.137-4.639a2.611,2.611,0,0,1,2.47-2.791,1.825,1.825,0,0,1,1.754,1.015Zm6.444-1.6a4.4,4.4,0,0,0-4.046-3.044,4.521,4.521,0,0,0-4.457,4.785,4.7,4.7,0,0,0,8.628,2.664l-1.611-1.088a2.692,2.692,0,0,1-2.328,1.323,2.4,2.4,0,0,1-2.291-1.449l6.319-2.646ZM76.154,35.315v2.029h4.8A4.268,4.268,0,0,1,79.86,39.9a4.886,4.886,0,0,1-3.706,1.485,5.4,5.4,0,0,1,0-10.8,5.078,5.078,0,0,1,3.616,1.45L81.184,30.6a6.951,6.951,0,0,0-5.03-2.049,7.431,7.431,0,1,0,0,14.862,6.665,6.665,0,0,0,5.119-2.085,6.757,6.757,0,0,0,1.737-4.749,6.637,6.637,0,0,0-.108-1.268Zm12.31,6.216a2.91,2.91,0,1,1,2.667-2.9A2.753,2.753,0,0,1,88.464,41.531Zm0-7.684a4.785,4.785,0,1,0,4.743,4.785A4.687,4.687,0,0,0,88.464,33.847Zm10.348,7.684a2.91,2.91,0,1,1,2.667-2.9A2.753,2.753,0,0,1,98.812,41.531Zm0-7.684a4.785,4.785,0,1,0,4.743,4.785A4.687,4.687,0,0,0,98.812,33.847Z" transform="translate(-23.105 -9.376)" fill="#fffffe" fill-rule="evenodd"/>
        <path id="Fill-20" d="M17.209,12.6a2.267,2.267,0,0,0-.513,1.579v24.86a2.267,2.267,0,0,0,.513,1.579l.082.081L31.046,26.775v-.328L17.291,12.521l-.082.081" transform="translate(-5.614 -4.111)" fill="#60c2e3" fill-rule="evenodd"/>
        <g id="Group-23" transform="translate(25.431 17.693)">
          <path id="Fill-21" d="M6.07,10.833,1.487,6.189V5.861L6.072,1.218,11.608,4.4c1.551.893,1.551,2.353,0,3.246L6.175,10.772l-.106.061" transform="translate(-1.487 -1.218)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        </g>
        <g id="Group-26" transform="translate(11.594 22.5)">
          <path id="Fill-24" d="M19.254,4.748,14.565,0,.728,14.01a1.791,1.791,0,0,0,2.308.069L19.254,4.748" transform="translate(-0.728)" fill-rule="evenodd" fill="url(#linear-gradient-2)"/>
        </g>
        <g id="Group-29" transform="translate(11.594 8.044)">
          <path id="Fill-27" d="M19.254,9.96,3.036.629A1.791,1.791,0,0,0,.728.7l13.837,14.01L19.254,9.96" transform="translate(-0.728 -0.252)" fill-rule="evenodd" fill="url(#linear-gradient-3)"/>
        </g>
      </g>
    </g>
  </g>
</svg>
