{"navItems": [{"label": "Home", "path": "#home", "offset": "70", "staticLink": false}, {"label": "Feature", "path": "#features", "offset": "70", "staticLink": false}, {"label": "Pricing", "path": "#pricing", "offset": "70", "staticLink": false}, {"label": "Dashboard", "path": "#dashboard", "offset": "70", "staticLink": false}, {"label": "Testimonials", "path": "#testimonials", "offset": "70", "staticLink": false}, {"label": "Login now", "path": "#", "offset": "70", "staticLink": true}, {"label": "Join Free", "path": "#", "offset": "70", "staticLink": true}], "features": [{"id": 1, "icon": "../../assets/image/saasMinimal2/features/1.png", "title": "Analytics Report", "desc": "We’re driven beyond just finishing the projects. We want to find smart solutions."}, {"id": 2, "icon": "../../assets/image/saasMinimal2/features/2.png", "title": "User Customization", "desc": "We’re driven beyond just finishing the projects. We want to find smart solutions."}, {"id": 3, "icon": "../../assets/image/saasMinimal2/features/3.png", "title": "Help & Support", "desc": "We’re driven beyond just finishing the projects. We want to find smart solutions."}, {"id": 4, "icon": "../../assets/image/saasMinimal2/features/4.png", "title": "Use Accessibility", "desc": "We’re driven beyond just finishing the projects. We want to find smart solutions."}], "clients": [{"id": 1, "logo": "../../assets/image/saasMinimal2/clients/1.png", "name": "<PERSON><PERSON>ck"}, {"id": 2, "logo": "../../assets/image/saasMinimal2/clients/2.png", "name": "<PERSON>"}, {"id": 3, "logo": "../../assets/image/saasMinimal2/clients/3.png", "name": "<PERSON><PERSON><PERSON>"}, {"id": 4, "logo": "../../assets/image/saasMinimal2/clients/4.png", "name": "Subway"}, {"id": 5, "logo": "../../assets/image/saasMinimal2/clients/5.png", "name": "Visao"}, {"id": 6, "logo": "../../assets/image/saasMinimal2/clients/6.png", "name": "OralUnic"}], "statistics": [{"id": 1, "value": 15, "url": "#", "title": "average increase in bookings"}, {"id": 2, "value": 73, "url": "#", "title": "increase in free account conversions"}, {"id": 3, "value": 28, "url": "#", "title": "increase in reservations"}], "dashboardProcess": [{"id": 1, "icon": "../../assets/image/saasMinimal2/dashboard/1.png", "title": "User Management"}, {"id": 2, "icon": "../../assets/image/saasMinimal2/dashboard/2.png", "title": "Online e-Commerce"}, {"id": 3, "icon": "../../assets/image/saasMinimal2/dashboard/3.png", "title": "Security & Privacy"}], "testimonials": [{"id": 1, "photo": "../../assets/image/saasMinimal2/testimonials/1.png", "rating": 4, "name": "<PERSON>", "designation": "CEO at Creativex", "text": "“OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design."}, {"id": 2, "photo": "../../assets/image/saasMinimal2/testimonials/2.png", "rating": 3, "name": "<PERSON><PERSON>", "designation": "COO at Uber co.", "text": "“My company has changed completely after I started using Coalize solutions. My employees are more motivated and I have managed to increase talent retention with the happiest service."}, {"id": 3, "photo": "../../assets/image/saasMinimal2/testimonials/1.png", "rating": 5, "name": "<PERSON><PERSON>", "designation": "COO at Uber co.", "text": "“My company has changed completely after I started using Coalize solutions. My employees are more motivated and I have managed to increase talent retention with the happiest service."}, {"id": 4, "photo": "../../assets/image/saasMinimal2/testimonials/2.png", "rating": 5, "name": "<PERSON><PERSON>", "designation": "COO at Uber co.", "text": "“My company has changed completely after I started using Coalize solutions. My employees are more motivated and I have managed to increase talent retention with the happiest service."}], "footerNav": [{"id": 1, "url": "#", "title": "Support"}, {"id": 2, "url": "#", "title": "About Us"}, {"id": 3, "url": "#", "title": "Privacy"}, {"id": 4, "url": "#", "title": "Contact"}], "socialLinks": [{"id": 1, "icon": "../../assets/image/saasMinimal2/social/fb.png", "link": "#", "label": "Facebook"}, {"id": 2, "icon": "../../assets/image/saasMinimal2/social/twitter.png", "link": "#", "label": "Twitter"}, {"id": 3, "icon": "../../assets/image/saasMinimal2/social/dribbble.png", "link": "#", "label": "<PERSON><PERSON><PERSON>"}]}