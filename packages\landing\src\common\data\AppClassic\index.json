{"navbar": {"logo": "../../assets/image/appClassic/logo.png", "navMenu": [{"id": 1, "label": "Home", "path": "#home", "offset": "84"}, {"id": 2, "label": "Key Features", "path": "#keyFeatures", "offset": "84"}, {"id": 3, "label": "Pricing", "path": "#pricing", "offset": "84"}, {"id": 4, "label": "Testimonial", "path": "#testimonial", "offset": "80"}, {"id": 5, "label": "Faq", "path": "#faq", "offset": "80"}]}, "client": [{"id": 1, "image": "../../assets/image/appClassic/client1.svg", "title": "The new york times"}, {"id": 2, "image": "../../assets/image/appClassic/client2.svg", "title": "amazon"}, {"id": 3, "image": "../../assets/image/appClassic/client3.svg", "title": "evernote"}, {"id": 4, "image": "../../assets/image/appClassic/client4.svg", "title": "the verge"}], "keyFeatures": {"slogan": "WHATS THE FUNCTION", "title": "Meet the feature of app", "features": [{"id": 1, "color": "#F55767", "icon": "../../assets/image/appClassic/keyFeature-1.svg", "title": "Fast Performance", "description": "Get your blood tests delivered at home collect a sample from the news your blood tests."}, {"id": 2, "color": "#2563FF", "icon": "../../assets/image/appClassic/keyFeature-2.svg", "title": "Prototyping", "description": "Get your blood tests delivered at home collect a sample from the news your blood tests."}, {"id": 3, "color": "#40975F", "icon": "../../assets/image/appClassic/keyFeature-3.svg", "title": "Vector Editing", "description": "Get your blood tests delivered at home collect a sample from the news your blood tests."}]}, "appSlider": {"carousel": [{"id": 1, "image": "../../assets/image/appClassic/appSlider1.png", "title": "App Slide 1"}, {"id": 2, "image": "../../assets/image/appClassic/appSlider2.png", "title": "App Slide 1"}, {"id": 3, "image": "../../assets/image/appClassic/appSlider3.png", "title": "App Slide 1"}], "title": "Smart Jackpots that you may love this anytime & anywhere", "description": "The rise of mobile devices transforms the way we consume information entirely and the world's most elevant channels such as Facebook.", "features": [{"id": 1, "icon": "flaticon-bitcoin", "title": "Automatic Payouts", "description": "Casinos no longer control the payout process."}, {"id": 2, "icon": "flaticon-atom", "title": "Network Effect", "description": "Big player rewards with the added security and transparency provided by the blockchain."}, {"id": 3, "icon": "flaticon-money-bag", "title": "Bigger Rewards Method", "description": "Casinos contribute 1% of wagers to decentralised prize pool Players are incentivized to play more to win bigger rewards."}]}, "features": {"slogan": "PRODUCT COMPARISON", "title": "Why you choose our App", "items": [{"id": 1, "color": "#F55767", "icon": "../../assets/image/appClassic/featureIcon-1.svg", "title": "App Development", "description": "Get your proof tests delivered home collect a sample from the news get design."}, {"id": 2, "color": "#3DABDD", "icon": "../../assets/image/appClassic/featureIcon-2.svg", "title": "10 Times Award", "description": "Get your proof tests delivered home collect a sample from the news get design."}, {"id": 3, "color": "#D6AB00", "icon": "../../assets/image/appClassic/featureIcon-3.svg", "title": "Cloud Storage", "description": "Get your proof tests delivered home collect a sample from the news get design."}, {"id": 4, "color": "#40975F", "icon": "../../assets/image/appClassic/featureIcon-4.svg", "title": "Customization", "description": "Get your proof tests delivered home collect a sample from the news get design."}, {"id": 5, "color": "#5856D6", "icon": "../../assets/image/appClassic/featureIcon-5.svg", "title": "UX Planning", "description": "Get your proof tests delivered home collect a sample from the news get design."}, {"id": 6, "color": "#E97325", "icon": "../../assets/image/appClassic/featureIcon-6.svg", "title": "Customer Support", "description": "Get your proof tests delivered home collect a sample from the news get design."}]}, "designAndBuilt": {"image": "../../assets/image/appClassic/appAndMap.png", "title": "Designed & Built by the latest code integration", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore features that Lorem ipsum dolor sit amet consectetur."}, "featuresTab": {"slogan": "DIFFERENTIATION", "title": "Ultimate features that we built", "tab": [{"id": 1, "color": "#F55767", "icon": "../../assets/image/appClassic/appTabIcon1.svg", "title": "App Development", "description": "Get your proof tests delivered home collect a sample.", "image": "../../assets/image/appClassic/appTabImg1.png"}, {"id": 2, "color": "#40975F", "icon": "../../assets/image/appClassic/appTabIcon2.svg", "title": "Customization", "description": "Get your proof tests delivered home collect a sample.", "image": "../../assets/image/appClassic/appTabImg2.png"}, {"id": 3, "color": "#5856D6", "icon": "../../assets/image/appClassic/appTabIcon3.svg", "title": "UX Planning", "description": "Get your proof tests delivered home collect a sample.", "image": "../../assets/image/appClassic/appTabImg3.png"}, {"id": 4, "color": "#D6AB00", "icon": "../../assets/image/appClassic/appTabIcon4.svg", "title": "Cloud Storage", "description": "Get your proof tests delivered home collect a sample.", "image": "../../assets/image/appClassic/appTabImg4.png"}, {"id": 5, "color": "#E97325", "icon": "../../assets/image/appClassic/appTabIcon5.svg", "title": "Customer Support", "description": "Get your proof tests delivered home collect a sample.", "image": "../../assets/image/appClassic/appTabImg5.png"}, {"id": 6, "color": "#3DABDD", "icon": "../../assets/image/appClassic/appTabIcon6.svg", "title": "10 Times Award", "description": "Get your proof tests delivered home collect a sample.", "image": "../../assets/image/appClassic/appTabImg6.png"}]}, "pricing": {"slogan": "PRICING PLAN", "title": "Choose your pricing policy", "monthly": [{"id": 1, "title": "Business Class", "description": "For Small teams or office", "suggested": false, "price": 0, "features": [{"id": 1, "text": "Drag & Drop Builder"}, {"id": 2, "text": "1,000's of Templates"}, {"id": 3, "text": "Blog Support Tools"}, {"id": 4, "text": "eCommerce Store "}]}, {"id": 2, "title": "Pro Master", "description": "For Best opportunities", "suggested": true, "price": 99, "trail": 14, "trailLink": "#", "features": [{"id": 1, "text": "Drag & Drop Builder"}, {"id": 2, "text": "1,000's of Templates"}, {"id": 3, "text": "Blog Support Tools"}, {"id": 4, "text": "eCommerce Store "}]}], "annualy": [{"id": 1, "title": "Pro Master", "description": "For Small teams or office", "suggested": true, "price": 999, "trail": 14, "trailLink": "#", "features": [{"id": 1, "text": "Drag & Drop Builder"}, {"id": 2, "text": "1,000's of Templates"}, {"id": 3, "text": "Blog Support Tools"}, {"id": 4, "text": "eCommerce Store "}]}, {"id": 2, "title": "Enterprise", "description": "For Best opportunities", "suggested": false, "price": 1299, "trail": 30, "trailLink": "#", "features": [{"id": 1, "text": "Drag & Drop Builder"}, {"id": 2, "text": "1,000's of Templates"}, {"id": 3, "text": "Blog Support Tools"}, {"id": 4, "text": "eCommerce Store "}]}]}, "testimonial": {"slogan": "TESTIMONIAL", "title": "Meet Client Satisfaction by using product", "reviews": [{"id": 1, "title": "Modern look & trending design", "description": "Get working experience to work with this amazing team & in future want to work together for bright future projects and also make deposit to freelancer.", "avatar": "https://pbs.twimg.com/profile_images/974736784906248192/gPZwCbdS.jpg", "name": "<PERSON>", "designation": "CEO of RedQ Inc.", "review": 4}, {"id": 2, "title": "User friendly & Customizable", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore features Lorem ipsum dolor sit amet consectetur adipisicing.", "avatar": "https://randomuser.me/api/portraits/women/44.jpg", "name": "<PERSON><PERSON>", "designation": "Co Founder of RedQ Inc.", "review": 5}, {"id": 3, "title": "User friendly & Customizable", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore features Lorem ipsum dolor sit amet consectetur adipisicing.", "avatar": "https://tinyfac.es/data/avatars/475605E3-69C5-4D2B-8727-61B7BB8C4699-500w.jpeg", "name": "<PERSON>", "designation": "Co Founder of RedQ Inc.", "review": 5}]}, "faq": {"slogan": "FREQUENT QUESTION", "title": "Do you have any question", "faqs": [{"id": 1, "question": "How to contact with riders emergency?", "answer": "Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home. Your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news."}, {"id": 2, "question": "App installation failed, how to update system information?", "answer": "Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home. Your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news."}, {"id": 3, "question": "Website reponse taking time, how to improve?", "answer": "Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home. Your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news."}, {"id": 4, "question": "New update fixed all bug and issues", "answer": "Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home. Your blood tests delivered at the home collect a sample from management news. Get your blood tests delivered at the home collect a sample from management news."}]}, "joinSlack": {"logo": "../../assets/image/appClassic/slack.png", "title": "Start your 30 days free trail today!", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore."}, "footer": {"widgets": [{"id": 1, "icon": "../../assets/image/appClassic/chat.svg", "title": "Join the Community", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore."}, {"id": 2, "icon": "../../assets/image/appClassic/group.svg", "title": "Join in Chat Community", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore."}, {"id": 3, "icon": "../../assets/image/appClassic/github.svg", "title": "Github Access", "description": "Lorem ipsum dolor sit amet consectetur adipisicing elit sed eiusmod tempor incididunt labore dolore."}], "logo": "../../assets/image/appClassic/logoWhite.png", "menu": [{"id": 1, "text": "Home", "link": "#"}, {"id": 2, "text": "Adversite", "link": "#"}, {"id": 3, "text": "Supports", "link": "#"}, {"id": 4, "text": "Marketing", "link": "#"}, {"id": 5, "text": "Contact", "link": "#"}]}}