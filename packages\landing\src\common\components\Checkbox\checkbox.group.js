// import React, { useState, Fragment } from 'react';
// import PropTypes from 'prop-types';
// import CheckBoxStyle from './checkbox.style';
// import CheckBox from './index';

// const CheckBoxGroup = props => {
//   const { data } = props;
//   console.log(data);
//   return (
//     <Fragment>
//       {Object.keys(data).forEach((key, index) => {
//         // console.log(key, data[key], 'moma');
//         <CheckBox id={key} labelText={key} />;
//       })}
//     </Fragment>
//   );
// };
// export default CheckBoxGroup;
