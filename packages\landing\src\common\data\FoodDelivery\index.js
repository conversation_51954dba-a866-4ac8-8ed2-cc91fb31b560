import callToActionImage from 'common/assets/image/foodDelivery/call-to-action-1.png';
export const CALL_TO_ACTION_DATA = {
  title: 'Your order knocking on \nthe door. Please receive',
  text: 'Pick one of our stock themes, or create your custom theme \nwith the most advanced theme editor on any online.',
  image: callToActionImage,
  link: {
    label: 'Order More',
    path: '#',
  },
};

import galleryImage1 from 'common/assets/image/foodDelivery/gallery-1.png';
import galleryImage2 from 'common/assets/image/foodDelivery/gallery-2.png';
import galleryImage3 from 'common/assets/image/foodDelivery/gallery-3.png';
import galleryImage4 from 'common/assets/image/foodDelivery/gallery-4.png';

export const GALLERY_DATA = {
  images: [
    {
      path: galleryImage1,
    },
    {
      path: galleryImage2,
    },
    {
      path: galleryImage3,
    },
    {
      path: galleryImage4,
    },
  ],
  title: 'Let’s take your Delivery Experience to the next level',
  text: 'Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool. Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool.',
};

import featureIcon1 from 'common/assets/image/foodDelivery/feature-icon-1.svg';
import featureIcon2 from 'common/assets/image/foodDelivery/feature-icon-2.svg';
import featureIcon3 from 'common/assets/image/foodDelivery/feature-icon-3.svg';
import featureIcon4 from 'common/assets/image/foodDelivery/feature-icon-4.svg';
import featureIcon5 from 'common/assets/image/foodDelivery/feature-icon-5.svg';
import featureIcon6 from 'common/assets/image/foodDelivery/feature-icon-6.svg';

export const QUALITY_FEATURES_DATA = {
  title: 'Meet our Quality Features',
  posts: [
    {
      icon: featureIcon1,
      title: 'Analytics Business',
      text: 'We’re driven beyond just finishing the projects. We want to find smart solutions.',
    },
    {
      icon: featureIcon2,
      title: 'Wide Coverage Map',
      text: 'We’re driven beyond just finishing the projects. We want to find smart solutions.',
    },
    {
      icon: featureIcon3,
      title: 'Artificial Intelligence',
      text: 'We’re driven beyond just finishing the projects. We want to find smart solutions.',
    },
    {
      icon: featureIcon4,
      title: 'Largest People',
      text: 'We’re driven beyond just finishing the projects. We want to find smart solutions.',
    },
    {
      icon: featureIcon5,
      title: 'Trusted & Secure',
      text: 'We’re driven beyond just finishing the projects. We want to find smart solutions.',
    },
    {
      icon: featureIcon6,
      title: 'Mobile Apps',
      text: 'We’re driven beyond just finishing the projects. We want to find smart solutions.',
    },
  ],
};

import downloadAppImage from 'common/assets/image/foodDelivery/download-app.png';
import downloadAppBtn1 from 'common/assets/image/foodDelivery/app-store.svg';
import downloadAppBtn2 from 'common/assets/image/foodDelivery/play-store.svg';

export const DOWNLOAD_APP_DATA = {
  sectionImage: downloadAppImage,
  title:
    'Connecting our user with iOS  & Android apps. Download from iTune & Play store',
  text: 'Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool. We’re driven beyond just finishing the projects. We want to find solutions using our website & apps.',
  buttons: [
    {
      icon: downloadAppBtn1,
      title: 'App Store',
      text: 'Download on the',
      link: '#',
    },
    {
      icon: downloadAppBtn2,
      title: 'Google Play',
      text: 'Download on the',
      link: '#',
    },
  ],
};

import client1 from 'common/assets/image/foodDelivery/client-1.png';
import client2 from 'common/assets/image/foodDelivery/client-2.png';
import client3 from 'common/assets/image/foodDelivery/client-3.png';
import client4 from 'common/assets/image/foodDelivery/client-4.png';
import client5 from 'common/assets/image/foodDelivery/client-5.png';
import client6 from 'common/assets/image/foodDelivery/client-6.png';
export const CLIENTS_DATA = {
  title: '210,000+ people already use our app on a daily basis',
  images: [
    {
      path: client1,
    },
    {
      path: client2,
    },
    {
      path: client3,
    },
    {
      path: client4,
    },
    {
      path: client5,
    },
    {
      path: client6,
    },
  ],
};

import deliveryProductImage from 'common/assets/image/foodDelivery/deliver-products.png';
export const DELIVERY_PRODUCT_DATA = {
  sectionContent: {
    title: 'We deliver our products as fast as superman can do',
    text: 'Pick one of our stock themes, or create your custom theme with the most advanced theme editor on any online survey building tool.',
    image: deliveryProductImage,
  },
  posts: [
    {
      title: 'Easy to use application',
      text: 'We’re driven beyond just finishing the projects. We want to find solutions using our website & apps.',
    },
    {
      title: 'Deliver Food within 30 min',
      text: 'We’re driven beyond just finishing the projects. We want to find solutions using our website & apps.',
    },
    {
      title: '100% Reliable with Privacy',
      text: 'We’re driven beyond just finishing the projects. We want to find solutions using our website & apps.',
    },
  ],
};

import howWorksImage1 from 'common/assets/image/foodDelivery/how-works-1.png';
import howWorksImage2 from 'common/assets/image/foodDelivery/how-works-2.png';
import howWorksImage3 from 'common/assets/image/foodDelivery/how-works-3.png';

export const HOW_WORKS_DATA = {
  title: 'Let’s see how it works',
  posts: [
    {
      icon: howWorksImage1,
      title: 'Become a Delivery Man',
      text: "As a delivery driver, you'll make reliable money—working anytime, anywhere.",
      link: {
        label: 'Start Earning',
        path: '#',
      },
    },
    {
      icon: howWorksImage2,
      title: 'Become a Partner',
      text: 'Grow your business and reach new customers by partnering with us.',
      link: {
        label: 'Sign up your store',
        path: '#',
      },
    },
    {
      icon: howWorksImage3,
      title: 'Try Android/iOS App',
      text: 'Get the best DoorDash experience with live order tracking.',
      link: {
        label: 'Get the app',
        path: '#',
      },
    },
  ],
};

export const PRODUCT_CATEGORY = {
  title: 'Popular categories by food',
  list: [
    {
      label: 'Pizza',
      link: '#',
    },
    {
      label: 'Breakfast',
      link: '#',
    },
    {
      label: 'Japanese',
      link: '#',
    },
    {
      label: 'Halal',
      link: '#',
    },
    {
      label: 'Dessert',
      link: '#',
    },
    {
      label: 'Lebanese',
      link: '#',
    },
    {
      label: 'American',
      link: '#',
    },
    {
      label: 'Sushi',
      link: '#',
    },
    {
      label: 'Greek',
      link: '#',
    },
    {
      label: 'Thai',
      link: '#',
    },
    {
      label: 'Vegetarian',
      link: '#',
    },
    {
      label: 'Italian',
      link: '#',
    },
    {
      label: 'Mexican',
      link: '#',
    },
    {
      label: 'Indian',
      link: '#',
    },
    {
      label: 'Chinese',
      link: '#',
    },
    {
      label: 'Breakfast',
      link: '#',
    },
    {
      label: 'Burgers',
      link: '#',
    },
  ],
};

import availableRestaurantImage1 from 'common/assets/image/foodDelivery/available-restaurant-1.png';
import availableRestaurantImage2 from 'common/assets/image/foodDelivery/available-restaurant-2.png';
import availableRestaurantImage3 from 'common/assets/image/foodDelivery/available-restaurant-3.png';
import availableRestaurantImage4 from 'common/assets/image/foodDelivery/available-restaurant-4.png';
import availableRestaurantImage5 from 'common/assets/image/foodDelivery/available-restaurant-5.png';
import availableRestaurantImage6 from 'common/assets/image/foodDelivery/available-restaurant-6.png';
export const AVAILABLE_RESTAURANTS_DATA = {
  title: 'Available Restaurant Nearby Area',
  posts: [
    {
      image: availableRestaurantImage1,
      title: 'Pizza Hut Delicious Pizza',
      link: '#',
      categories: [
        { name: 'American' },
        { name: 'Fast Food' },
        { name: 'Burgers' },
      ],
    },
    {
      image: availableRestaurantImage2,
      title: 'Chipotle Mexican Grill (2675 Geary Boulevard)',
      link: '#',
      categories: [
        { name: 'American' },
        { name: 'Fast Food' },
        { name: 'Burgers' },
      ],
    },
    {
      image: availableRestaurantImage3,
      title: "McDonald's® Burgers (Fillmore)",
      link: '#',
      categories: [
        { name: 'American' },
        { name: 'Fast Food' },
        { name: 'Burgers' },
      ],
    },
    {
      image: availableRestaurantImage4,
      title: 'The Baked Bear San Francisco',
      link: '#',
      categories: [
        { name: 'American' },
        { name: 'Fast Food' },
        { name: 'Burgers' },
      ],
    },
    {
      image: availableRestaurantImage5,
      title: 'Shake Shack  (3060 Fillmore Street)',
      link: '#',
      categories: [
        { name: 'American' },
        { name: 'Fast Food' },
        { name: 'Burgers' },
      ],
    },
    {
      image: availableRestaurantImage6,
      title: 'Chubby Noodle Chinese Takeout',
      link: '#',
      categories: [
        { name: 'American' },
        { name: 'Fast Food' },
        { name: 'Burgers' },
      ],
    },
  ],
};

import bannerImage from 'common/assets/image/foodDelivery/banner-1.png';
import bannerBtn1 from 'common/assets/image/foodDelivery/banner-apple.svg';
import bannerBtn2 from 'common/assets/image/foodDelivery/banner-google-play.svg';

export const BANNER_DATA = {
  sectionImage: bannerImage,
  title: 'Your favorite food, delivered your home',
  text: 'Food, drinks, groceries, and more available for delivery and pickup.',
  tagLine: 'Apps Available to download on',
  buttons: [
    {
      icon: bannerBtn1,
      title: 'App Store',
      text: 'Download on the',
      link: '#',
    },
    {
      icon: bannerBtn2,
      title: 'Google Play',
      text: 'Download on the',
      link: '#',
    },
  ],
};

export const MENU_ITEMS = [
  {
    label: 'Home',
    path: '#banner_section',
    offset: '70',
  },
  {
    label: 'Restaurants',
    path: '#restaurants',
    offset: '70',
  },
  {
    label: 'Features',
    path: '#features',
    offset: '70',
  },
  {
    label: 'Testimonials',
    path: '#testimonials',
    offset: '70',
  },
  {
    label: 'Download',
    path: '#download',
    offset: '70',
  },
];

import testimonialBlockImage from 'common/assets/image/foodDelivery/testimonials-block-image.png';
import testimonial1 from 'common/assets/image/foodDelivery/testimonials-1-1.png';
import testimonial2 from 'common/assets/image/foodDelivery/testimonials-1-2.png';
export const TESTIMONIALS_DATA = {
  blockImage: testimonialBlockImage,
  title: 'What people say about us',
  posts: [
    {
      image: testimonial1,
      text: 'OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design.',
      name: 'Mariana Dickey',
      designation: 'UI Designer',
    },
    {
      image: testimonial2,
      text: 'OMG! I cannot believe that I have got a brand new landing page after getting this template we are able to use our most used e-commerce template with modern and trending design.',
      name: 'Jonathan Taylor',
      designation: 'CEO at Creativex',
    },
  ],
};
